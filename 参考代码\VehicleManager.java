package com.autolink.sbjk.service;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.IBinder;
import android.os.RemoteException;
import android.util.Log;
import android.widget.Toast;

import com.autolink.app.vehicleservice.IVehicleControl;
import com.autolink.app.vehicleservice.IVehicleControlCallback;
import com.autolink.sbjk.utils.VehiclePropertyKey;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 车辆服务管理器，负责与VehicleService通信
 */
public class VehicleManager {
    private static final String TAG = "VehicleService";
    
    // 钥匙状态枚举
    public static final int KEY_STATE_OFF = 0;    // 关闭
    public static final int KEY_STATE_ACC = 1;    // 配件
    public static final int KEY_STATE_ON = 2;     // 打开
    public static final int KEY_STATE_CRANK = 3;  // 启动
    
    // 档位状态枚举
    public static final int GEAR_POSITION_R = 2;  // R档
    public static final int GEAR_POSITION_P = 1;  // P档
    public static final int GEAR_POSITION_N = 3;  // N档 
    public static final int GEAR_POSITION_D = 4;  // D档
    public static final int GEAR_POSITION_S = 50;  // S档
    public static final int GEAR_POSITION_L = 60;  // L档
    
    private static VehicleManager sInstance;
    private final Context mContext;
    private IVehicleControl mVehicleControl;
    private boolean mIsBound = false;
    private final List<VehicleDataListener> mListeners = new CopyOnWriteArrayList<>();
    
    // 单例模式
    public static synchronized VehicleManager getInstance(Context context) {
        if (sInstance == null) {
            sInstance = new VehicleManager(context.getApplicationContext());
        }
        return sInstance;
    }
    
    private VehicleManager(Context context) {
        mContext = context;
        Log.i(TAG, "VehicleManager初始化，准备绑定服务");
        Log.i(TAG, "当前进程ID: " + android.os.Process.myPid());
        bindVehicleService();
    }
    
    // 车辆数据监听器接口
    public interface VehicleDataListener {
        void onVehiclePropertyChanged(VehiclePropertyKey key, Object value, boolean selfGet);
    }
    
    // 添加监听器
    public void addListener(VehicleDataListener listener) {
        if (listener != null && !mListeners.contains(listener)) {
            mListeners.add(listener);
            Log.i(TAG, "添加车辆数据监听器: " + listener.getClass().getSimpleName());
        }
    }
    
    // 移除监听器
    public void removeListener(VehicleDataListener listener) {
        if (listener != null) {
            mListeners.remove(listener);
            Log.i(TAG, "移除车辆数据监听器: " + listener.getClass().getSimpleName());
        }
    }
    
    // 绑定车辆服务
    private void bindVehicleService() {
        if (mIsBound) {
            Log.i(TAG, "车辆服务已绑定，无需重复绑定");
            return;
        }
        
        try {
            Intent intent = new Intent();
            intent.setComponent(new ComponentName(
                    "com.autolink.app.vehicleservice",
                    "com.autolink.app.vehicleservice.VehicleService"));
            
            Log.i(TAG, "尝试绑定车辆服务: " + intent.getComponent().flattenToShortString());
            mIsBound = mContext.bindService(intent, mServiceConnection, Context.BIND_AUTO_CREATE);
            Log.i(TAG, "绑定车辆服务: " + (mIsBound ? "成功" : "失败"));
            
            if (!mIsBound) {
                Toast.makeText(mContext, "车辆服务绑定失败，请检查服务是否运行", Toast.LENGTH_LONG).show();
            }
        } catch (Exception e) {
            Log.e(TAG, "绑定车辆服务异常", e);
            Toast.makeText(mContext, "绑定车辆服务异常: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }
    
    // 解绑车辆服务
    public void unbindVehicleService() {
        if (mIsBound) {
            try {
                if (mVehicleControl != null && mCallback != null) {
                    mVehicleControl.unregisterCallback(mCallback);
                    Log.i(TAG, "已注销车辆属性回调");
                }
            } catch (RemoteException e) {
                Log.e(TAG, "解注册回调失败", e);
            }
            
            mContext.unbindService(mServiceConnection);
            mVehicleControl = null;
            mIsBound = false;
            Log.i(TAG, "解绑车辆服务");
        }
    }
    
    // 手动重新绑定服务
    public void rebindVehicleService() {
        Log.i(TAG, "手动重新绑定车辆服务");
        if (mIsBound) {
            unbindVehicleService();
        }
        bindVehicleService();
    }
    
    // 服务连接回调
    private final ServiceConnection mServiceConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            Log.i(TAG, "车辆服务已连接: " + name.flattenToShortString());
            try {
                mVehicleControl = IVehicleControl.Stub.asInterface(service);
                Log.i(TAG, "获取到IVehicleControl接口，准备注册回调");
                registerVehicleCallbacks();
            } catch (Exception e) {
                Log.e(TAG, "初始化车辆控制接口异常", e);
                Toast.makeText(mContext, "初始化车辆控制接口异常: " + e.getMessage(), Toast.LENGTH_LONG).show();
            }
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {
            Log.w(TAG, "车辆服务已断开: " + name.flattenToShortString());
            mVehicleControl = null;
            mIsBound = false;
            
            // 尝试重新绑定
            new Thread(() -> {
                try {
                    Log.i(TAG, "等待3秒后尝试重新绑定车辆服务");
                    Thread.sleep(3000);
                    bindVehicleService();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }).start();
        }
    };
    
    // 车辆服务回调实现
    private final IVehicleControlCallback mCallback = new IVehicleControlCallback.Stub() {
        @Override
        public void onReceiveInt(int propKey, int value, boolean selfGet) throws RemoteException {
            VehiclePropertyKey key = VehiclePropertyKey.INSTANCE.valueOf(propKey);
            if (key != null) {
                Log.i(TAG, "接收整型属性: " + key.getDesc() + " = " + value + ", selfGet=" + selfGet);
                
                // 对特定属性进行日志增强
                if (key == VehiclePropertyKey.KeyVehiclePropertyKeySts) {
                    Log.i(TAG, "钥匙状态详细信息: " + getKeyStateName(value) + " (值=" + value + ")");
                } else if (key == VehiclePropertyKey.KeyVehiclePropertyGearPosition) {
                    Log.i(TAG, "档位状态详细信息: " + getGearPositionName(value) + " (值=" + value + ")");
                }
                
                notifyListeners(key, value, selfGet);
            } else {
                Log.w(TAG, "接收到未知整型属性: propKey=0x" + Integer.toHexString(propKey) + ", value=" + value);
            }
        }

        @Override
        public void onReceiveFloat(int propKey, float value, boolean selfGet) throws RemoteException {
            VehiclePropertyKey key = VehiclePropertyKey.INSTANCE.valueOf(propKey);
            if (key != null) {
                Log.i(TAG, "接收浮点型属性: " + key.getDesc() + " = " + value + ", selfGet=" + selfGet);
                notifyListeners(key, value, selfGet);
            } else {
                Log.w(TAG, "接收到未知浮点型属性: propKey=0x" + Integer.toHexString(propKey) + ", value=" + value);
            }
        }

        @Override
        public void onOnlySyncIntData(int propKey, int value) throws RemoteException {
            VehiclePropertyKey key = VehiclePropertyKey.INSTANCE.valueOf(propKey);
            if (key != null) {
                Log.i(TAG, "接收同步整型属性: " + key.getDesc() + " = " + value);
                notifyListeners(key, value, false);
            } else {
                Log.w(TAG, "接收到未知同步整型属性: propKey=0x" + Integer.toHexString(propKey) + ", value=" + value);
            }
        }

        @Override
        public void onOnlySyncFloatData(int propKey, float value) throws RemoteException {
            VehiclePropertyKey key = VehiclePropertyKey.INSTANCE.valueOf(propKey);
            if (key != null) {
                Log.i(TAG, "接收同步浮点型属性: " + key.getDesc() + " = " + value);
                notifyListeners(key, value, false);
            } else {
                Log.w(TAG, "接收到未知同步浮点型属性: propKey=0x" + Integer.toHexString(propKey) + ", value=" + value);
            }
        }
    };
    
    // 通知所有监听器
    private void notifyListeners(VehiclePropertyKey key, Object value, boolean selfGet) {
        if (mListeners.isEmpty()) {
            Log.i(TAG, "没有注册的监听器，不通知车辆数据变化");
            return;
        }
        
        for (VehicleDataListener listener : mListeners) {
            try {
                listener.onVehiclePropertyChanged(key, value, selfGet);
            } catch (Exception e) {
                Log.e(TAG, "通知监听器失败: " + listener.getClass().getSimpleName(), e);
            }
        }
    }
    
    // 注册车辆回调
    private void registerVehicleCallbacks() {
        if (mVehicleControl == null) {
            Log.e(TAG, "车辆服务未连接，无法注册回调");
            return;
        }
        
        try {
            // 定义需要监听的属性，重点关注档位和电源信息
            int[] propertyKeys = new int[] {
                // 电源状态
                VehiclePropertyKey.KeyVehiclePropertyKeySts.getValue(),      // 钥匙状态
                VehiclePropertyKey.KeyVehiclePropertyEngineSts.getValue(),   // 发动机状态
                
                // 档位状态
                VehiclePropertyKey.KeyVehiclePropertyGearPosition.getValue(), // 当前档位
                
                // 车速（可选）
                VehiclePropertyKey.KeyVehiclePropertySpeed.getValue(),       // 车速
            };
            
            Log.i(TAG, "注册车辆属性回调，属性数量: " + propertyKeys.length);
            for (int propKey : propertyKeys) {
                VehiclePropertyKey key = VehiclePropertyKey.INSTANCE.valueOf(propKey);
                if (key != null) {
                    Log.i(TAG, "注册属性: " + key.getDesc() + " (0x" + Integer.toHexString(key.getValue()) + ")");
                }
            }
            
            // 注册回调
            mVehicleControl.registerCallback(mCallback, propertyKeys);
            Log.i(TAG, "已注册车辆属性回调");
            
            // 主动获取一次当前属性值
            Log.i(TAG, "主动获取当前属性值");
            for (int propKey : propertyKeys) {
                try {
                    VehiclePropertyKey key = VehiclePropertyKey.INSTANCE.valueOf(propKey);
                    if (key != null) {
                        int intValue = mVehicleControl.getInt(propKey);
                        
                        // 增强特定属性的日志输出
                        if (key == VehiclePropertyKey.KeyVehiclePropertyKeySts) {
                            Log.i(TAG, "初始获取钥匙状态: " + getKeyStateName(intValue) + " (值=" + intValue + ")");
                        } else if (key == VehiclePropertyKey.KeyVehiclePropertyGearPosition) {
                            Log.i(TAG, "初始获取档位状态: " + getGearPositionName(intValue) + " (值=" + intValue + ")");
                        } else {
                            Log.i(TAG, "初始获取属性: " + key.getDesc() + " = " + intValue);
                        }
                    }
                } catch (Exception e) {
                    Log.w(TAG, "获取属性值异常: propKey=0x" + Integer.toHexString(propKey), e);
                }
            }
            
        } catch (RemoteException e) {
            Log.e(TAG, "注册车辆属性回调失败", e);
            Toast.makeText(mContext, "注册车辆属性回调失败: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }
    
    // 获取整型属性
    public int getIntProperty(VehiclePropertyKey key) {
        if (mVehicleControl == null || key == null) {
            Log.e(TAG, "车辆服务未连接或属性键为空");
            return -1;
        }
        
        try {
            int value = mVehicleControl.getInt(key.getValue());
            // 对特定属性进行日志增强
            if (key == VehiclePropertyKey.KeyVehiclePropertyKeySts) {
                Log.i(TAG, "获取钥匙状态: " + getKeyStateName(value) + " (值=" + value + ")");
            } else if (key == VehiclePropertyKey.KeyVehiclePropertyGearPosition) {
                Log.i(TAG, "获取档位状态: " + getGearPositionName(value) + " (值=" + value + ")");
            } else {
                Log.i(TAG, "获取整型属性: " + key.getDesc() + " = " + value);
            }
            return value;
        } catch (RemoteException e) {
            Log.e(TAG, "获取整型属性失败: " + key.getDesc(), e);
            return -1;
        }
    }
    
    // 获取浮点型属性
    public float getFloatProperty(VehiclePropertyKey key) {
        if (mVehicleControl == null || key == null) {
            Log.e(TAG, "车辆服务未连接或属性键为空");
            return -1.0f;
        }
        
        try {
            float value = mVehicleControl.getFloat(key.getValue());
            Log.i(TAG, "获取浮点型属性: " + key.getDesc() + " = " + value);
            return value;
        } catch (RemoteException e) {
            Log.e(TAG, "获取浮点型属性失败: " + key.getDesc(), e);
            return -1.0f;
        }
    }
    
    // 设置整型属性
    public boolean setIntProperty(VehiclePropertyKey key, int value) {
        if (mVehicleControl == null || key == null) {
            Log.e(TAG, "车辆服务未连接或属性键为空");
            return false;
        }
        
        try {
            mVehicleControl.setInt(key.getValue(), value);
            // 对特定属性进行日志增强
            if (key == VehiclePropertyKey.KeyVehiclePropertyKeySts) {
                Log.i(TAG, "设置钥匙状态: " + getKeyStateName(value) + " (值=" + value + ")");
            } else if (key == VehiclePropertyKey.KeyVehiclePropertyGearPosition) {
                Log.i(TAG, "设置档位状态: " + getGearPositionName(value) + " (值=" + value + ")");
            } else {
                Log.i(TAG, "设置整型属性: " + key.getDesc() + " = " + value);
            }
            return true;
        } catch (RemoteException e) {
            Log.e(TAG, "设置整型属性失败: " + key.getDesc(), e);
            return false;
        }
    }
    
    // 设置浮点型属性
    public boolean setFloatProperty(VehiclePropertyKey key, float value) {
        if (mVehicleControl == null || key == null) {
            Log.e(TAG, "车辆服务未连接或属性键为空");
            return false;
        }
        
        try {
            mVehicleControl.setFloat(key.getValue(), value);
            Log.i(TAG, "设置浮点型属性: " + key.getDesc() + " = " + value);
            return true;
        } catch (RemoteException e) {
            Log.e(TAG, "设置浮点型属性失败: " + key.getDesc(), e);
            return false;
        }
    }
    
    // 获取当前档位信息
    public String getGearPositionName(int gearPosition) {
        switch (gearPosition) {
            case GEAR_POSITION_P:
                return "P档";
            case GEAR_POSITION_R:
                return "R档";
            case GEAR_POSITION_N:
                return "N档";
            case GEAR_POSITION_D:
                return "D档";
            case GEAR_POSITION_S:
                return "S档";
            case GEAR_POSITION_L:
                return "L档";
            default:
                return "未知档位(" + gearPosition + ")";
        }
    }
    
    // 获取钥匙状态名称
    public String getKeyStateName(int keyState) {
        switch (keyState) {
            case KEY_STATE_OFF:
                return "钥匙OFF";
            case KEY_STATE_ACC:
                return "钥匙ACC";
            case KEY_STATE_ON:
                return "钥匙ON";
            case KEY_STATE_CRANK:
                return "钥匙CRANK";
            default:
                return "未知状态(" + keyState + ")";
        }
    }
    
    // 获取发动机状态名称
    public String getEngineStateName(int engineState) {
        return engineState == 1 ? "发动机运行中" : "发动机停止";
    }
    
    // 检查是否已连接
    public boolean isConnected() {
        return mIsBound && mVehicleControl != null;
    }
} 