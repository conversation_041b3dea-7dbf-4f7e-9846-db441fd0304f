package com.autolink.sbjk.model.repository;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;

import com.autolink.sbjk.common.constant.CameraConstants;
import com.autolink.sbjk.model.datasource.CameraDataSource;
import com.autolink.sbjk.model.entity.CameraInfo;
import com.autolink.sbjk.model.state.CameraState;
import com.autolink.sbjk.common.util.LogUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 相机数据仓储
 * 统一管理相机数据访问和状态
 */
public class CameraRepository {


    
    private static final String TAG = "CameraRepository";
    private static volatile CameraRepository instance;
    
    private final CameraDataSource cameraDataSource;
    private final ExecutorService executorService;
    private final Map<String, CameraInfo> cameraInfoCache;

    // 录制分段完成回调
    private com.autolink.sbjk.common.callback.SegmentCompletionCallback segmentCompletionCallback;
    
    // LiveData for observing camera state
    private final MutableLiveData<CameraState> _cameraState = new MutableLiveData<>();
    public final LiveData<CameraState> cameraState = _cameraState;
    
    private final MutableLiveData<Map<String, CameraInfo>> _cameraInfoMap = new MutableLiveData<>();
    public final LiveData<Map<String, CameraInfo>> cameraInfoMap = _cameraInfoMap;
    
    private CameraRepository(Context context) {
        this.cameraDataSource = new CameraDataSource(context);
        this.executorService = Executors.newCachedThreadPool();
        this.cameraInfoCache = new ConcurrentHashMap<>();

        // 设置分段完成回调
        this.cameraDataSource.setSegmentCompletionCallback((cameraId, segmentPath) -> {
            if (segmentCompletionCallback != null) {
                segmentCompletionCallback.onSegmentCompleted(cameraId, segmentPath);
            }
        });

        initializeCameraInfo();
    }
    
    public static CameraRepository getInstance(Context context) {
        if (instance == null) {
            synchronized (CameraRepository.class) {
                if (instance == null) {
                    instance = new CameraRepository(context.getApplicationContext());
                }
            }
        }
        return instance;
    }
    
    /**
     * 初始化相机信息
     */
    private void initializeCameraInfo() {
        for (String cameraId : CameraConstants.CAMERA_IDS) {
            CameraInfo cameraInfo = new CameraInfo(cameraId);
            cameraInfoCache.put(cameraId, cameraInfo);
        }
        
        updateCameraState();
        LogUtil.d(TAG, "Camera info initialized for " + cameraInfoCache.size() + " cameras");
    }
    
    /**
     * 获取相机信息
     */
    public CameraInfo getCameraInfo(String cameraId) {
        return cameraInfoCache.get(cameraId);
    }
    
    /**
     * 获取所有相机信息
     */
    public Map<String, CameraInfo> getAllCameraInfo() {
        return new HashMap<>(cameraInfoCache);
    }

    /**
     * 设置录制分段完成回调
     */
    public void setSegmentCompletionCallback(com.autolink.sbjk.common.callback.SegmentCompletionCallback callback) {
        this.segmentCompletionCallback = callback;
    }
    
    /**
     * 更新相机状态
     */
    public void updateCameraStatus(String cameraId, CameraConstants.CameraStatus status) {
        executorService.execute(() -> {
            try {
                CameraInfo cameraInfo = cameraInfoCache.get(cameraId);
                if (cameraInfo != null) {
                    cameraInfo.setStatus(status);
                    LogUtil.d(TAG, "Camera " + cameraId + " status updated to " + status);
                    updateCameraState();
                } else {
                    LogUtil.w(TAG, "Camera info not found for " + cameraId);
                }
            } catch (Exception e) {
                LogUtil.e(TAG, "Exception updating camera status for " + cameraId, e);
                setCameraError(cameraId, "状态更新异常: " + e.getMessage());
            }
        });
    }

    /**
     * 更新相机录制状态
     */
    public void updateRecordingStatus(String cameraId, boolean isRecording) {
        executorService.execute(() -> {
            try {
                CameraInfo cameraInfo = cameraInfoCache.get(cameraId);
                if (cameraInfo != null) {
                    cameraInfo.setRecording(isRecording);
                    if (isRecording) {
                        cameraInfo.setRecordingStartTime(System.currentTimeMillis());
                        cameraInfo.setStatus(CameraConstants.CameraStatus.RECORDING);
                    } else {
                        cameraInfo.setStatus(CameraConstants.CameraStatus.IDLE);
                    }
                    LogUtil.d(TAG, "Camera " + cameraId + " recording status: " + isRecording);
                    updateCameraState();
                } else {
                    LogUtil.w(TAG, "Camera info not found for " + cameraId);
                }
            } catch (Exception e) {
                LogUtil.e(TAG, "Exception updating recording status for " + cameraId, e);
                setCameraError(cameraId, "录制状态更新异常: " + e.getMessage());
            }
        });
    }
    
    /**
     * 设置相机错误
     */
    public void setCameraError(String cameraId, String error) {
        executorService.execute(() -> {
            CameraInfo cameraInfo = cameraInfoCache.get(cameraId);
            if (cameraInfo != null) {
                cameraInfo.setLastError(error);
                cameraInfo.setStatus(CameraConstants.CameraStatus.ERROR);
                LogUtil.e(TAG, "Camera " + cameraId + " error: " + error);
                updateCameraState();
            }
        });
    }
    
    /**
     * 清除相机错误
     */
    public void clearCameraError(String cameraId) {
        executorService.execute(() -> {
            CameraInfo cameraInfo = cameraInfoCache.get(cameraId);
            if (cameraInfo != null) {
                cameraInfo.setLastError(null);
                if (cameraInfo.getStatus() == CameraConstants.CameraStatus.ERROR) {
                    cameraInfo.setStatus(CameraConstants.CameraStatus.IDLE);
                }
                LogUtil.d(TAG, "Camera " + cameraId + " error cleared");
                updateCameraState();
            }
        });
    }
    
    /**
     * 开始录制
     */
    public void startRecording(String cameraId, String outputPath) {
        executorService.execute(() -> {
            try {
                if (!CameraConstants.isValidCameraId(cameraId)) {
                    LogUtil.e(TAG, "Invalid camera ID: " + cameraId);
                    setCameraError(cameraId, "无效的相机ID");
                    return;
                }

                CameraInfo cameraInfo = cameraInfoCache.get(cameraId);
                if (cameraInfo == null) {
                    LogUtil.e(TAG, "Camera info not found for " + cameraId);
                    setCameraError(cameraId, "相机信息未找到");
                    return;
                }

                if (!cameraInfo.canStartRecording()) {
                    LogUtil.w(TAG, "Camera " + cameraId + " cannot start recording, current status: " + cameraInfo.getStatus());
                    setCameraError(cameraId, "相机当前状态不允许开始录制");
                    return;
                }

                cameraInfo.setStatus(CameraConstants.CameraStatus.INITIALIZING);
                updateCameraState();

                // 调用数据源开始录制
                boolean success = cameraDataSource.startRecording(cameraId, outputPath);
                if (success) {
                    cameraInfo.setRecording(true);
                    cameraInfo.setRecordingStartTime(System.currentTimeMillis());
                    cameraInfo.setCurrentSegmentPath(outputPath);
                    cameraInfo.setStatus(CameraConstants.CameraStatus.RECORDING);
                    cameraInfo.setLastError(null); // 清除之前的错误
                    LogUtil.i(TAG, "Recording started for camera " + cameraId);
                } else {
                    cameraInfo.setStatus(CameraConstants.CameraStatus.ERROR);
                    cameraInfo.setLastError("启动录制失败");
                    LogUtil.e(TAG, "Failed to start recording for camera " + cameraId);
                }
                updateCameraState();

            } catch (Exception e) {
                LogUtil.e(TAG, "Exception starting recording for camera " + cameraId, e);
                setCameraError(cameraId, "录制启动异常: " + e.getMessage());
            }
        });
    }
    
    /**
     * 停止录制
     */
    public void stopRecording(String cameraId) {
        executorService.execute(() -> {
            try {
                if (!CameraConstants.isValidCameraId(cameraId)) {
                    LogUtil.e(TAG, "Invalid camera ID: " + cameraId);
                    setCameraError(cameraId, "无效的相机ID");
                    return;
                }

                CameraInfo cameraInfo = cameraInfoCache.get(cameraId);
                if (cameraInfo == null) {
                    LogUtil.e(TAG, "Camera info not found for " + cameraId);
                    setCameraError(cameraId, "相机信息未找到");
                    return;
                }

                if (!cameraInfo.canStopRecording()) {
                    LogUtil.w(TAG, "Camera " + cameraId + " cannot stop recording, current status: " + cameraInfo.getStatus());
                    // 不设置错误，因为可能已经停止了
                    return;
                }

                // 调用数据源停止录制
                boolean success = cameraDataSource.stopRecording(cameraId);
                if (success) {
                    cameraInfo.setRecording(false);
                    cameraInfo.setStatus(CameraConstants.CameraStatus.IDLE);
                    cameraInfo.setLastError(null); // 清除之前的错误
                    LogUtil.i(TAG, "Recording stopped for camera " + cameraId);
                } else {
                    cameraInfo.setLastError("停止录制失败");
                    LogUtil.e(TAG, "Failed to stop recording for camera " + cameraId);
                }
                updateCameraState();

            } catch (Exception e) {
                LogUtil.e(TAG, "Exception stopping recording for camera " + cameraId, e);
                setCameraError(cameraId, "录制停止异常: " + e.getMessage());
            }
        });
    }
    
    /**
     * 开始所有相机录制
     */
    public void startAllRecording(String basePath) {
        for (String cameraId : CameraConstants.CAMERA_IDS) {
            String outputPath = basePath + "/" + CameraConstants.getCameraName(cameraId);
            startRecording(cameraId, outputPath);
        }
    }
    
    /**
     * 停止所有相机录制
     */
    public void stopAllRecording() {
        for (String cameraId : CameraConstants.CAMERA_IDS) {
            stopRecording(cameraId);
        }
    }

    /**
     * 预览Surface管理方法
     */
    public void setPreviewSurface(String cameraId, android.view.Surface surface) {
        executorService.execute(() -> {
            try {
                if (!CameraConstants.isValidCameraId(cameraId)) {
                    LogUtil.e(TAG, "Invalid camera ID for preview surface: " + cameraId);
                    return;
                }

                // 验证Surface有效性
                if (surface != null && !surface.isValid()) {
                    LogUtil.w(TAG, "Surface is invalid for camera " + cameraId + ", will retry");
                    // 延迟重试
                    retrySetPreviewSurface(cameraId, surface, 3);
                    return;
                }

                boolean success = cameraDataSource.setPreviewSurface(cameraId, surface);
                if (success) {
                    LogUtil.d(TAG, "Preview surface set for camera " + cameraId);
                } else {
                    LogUtil.e(TAG, "Failed to set preview surface for camera " + cameraId);
                    // 重试设置
                    retrySetPreviewSurface(cameraId, surface, 2);
                }
            } catch (Exception e) {
                LogUtil.e(TAG, "Exception setting preview surface for camera " + cameraId, e);
                setCameraError(cameraId, "预览Surface设置异常: " + e.getMessage());
            }
        });
    }

    /**
     * 重试设置预览Surface
     */
    private void retrySetPreviewSurface(String cameraId, android.view.Surface surface, int retryCount) {
        if (retryCount > 0) {
            // 延迟1秒后重试
            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                executorService.execute(() -> {
                    try {
                        if (surface != null && surface.isValid()) {
                            boolean success = cameraDataSource.setPreviewSurface(cameraId, surface);
                            if (success) {
                                LogUtil.i(TAG, "Preview surface retry successful for camera " + cameraId);
                            } else if (retryCount > 1) {
                                retrySetPreviewSurface(cameraId, surface, retryCount - 1);
                            } else {
                                LogUtil.e(TAG, "Preview surface retry failed for camera " + cameraId);
                                setCameraError(cameraId, "设置预览Surface重试失败");
                            }
                        } else {
                            LogUtil.w(TAG, "Surface became invalid during retry for camera " + cameraId);
                        }
                    } catch (Exception e) {
                        LogUtil.e(TAG, "Exception during preview surface retry for camera " + cameraId, e);
                    }
                });
            }, 1000);
        }
    }

    public void updatePreviewSurface(String cameraId, android.view.Surface surface) {
        executorService.execute(() -> {
            try {
                if (!CameraConstants.isValidCameraId(cameraId)) {
                    LogUtil.e(TAG, "Invalid camera ID for preview surface update: " + cameraId);
                    return;
                }

                boolean success = cameraDataSource.setPreviewSurface(cameraId, surface);
                if (success) {
                    LogUtil.d(TAG, "Preview surface updated for camera " + cameraId);
                } else {
                    LogUtil.e(TAG, "Failed to update preview surface for camera " + cameraId);
                    setCameraError(cameraId, "更新预览Surface失败");
                }
            } catch (Exception e) {
                LogUtil.e(TAG, "Exception updating preview surface for camera " + cameraId, e);
                setCameraError(cameraId, "预览Surface更新异常: " + e.getMessage());
            }
        });
    }

    public void clearPreviewSurface(String cameraId) {
        executorService.execute(() -> {
            try {
                if (!CameraConstants.isValidCameraId(cameraId)) {
                    LogUtil.e(TAG, "Invalid camera ID for preview surface clear: " + cameraId);
                    return;
                }

                boolean success = cameraDataSource.setPreviewSurface(cameraId, null);
                if (success) {
                    LogUtil.d(TAG, "Preview surface cleared for camera " + cameraId);
                } else {
                    LogUtil.e(TAG, "Failed to clear preview surface for camera " + cameraId);
                    // 清除预览Surface失败不设置错误，因为可能是正常的清理过程
                }
            } catch (Exception e) {
                LogUtil.e(TAG, "Exception clearing preview surface for camera " + cameraId, e);
                // 清除预览Surface异常也不设置错误，避免影响正常流程
            }
        });
    }

    /**
     * 暂停所有预览但保持录制
     */
    public void pauseAllPreviews() {
        executorService.execute(() -> {
            try {
                LogUtil.d(TAG, "Pausing all camera previews");
                for (String cameraId : CameraConstants.CAMERA_IDS) {
                    cameraDataSource.pausePreview(cameraId);
                }
            } catch (Exception e) {
                LogUtil.e(TAG, "Exception pausing all previews", e);
            }
        });
    }

    /**
     * 恢复所有预览
     */
    public void resumeAllPreviews() {
        executorService.execute(() -> {
            try {
                LogUtil.d(TAG, "Resuming all camera previews");
                for (String cameraId : CameraConstants.CAMERA_IDS) {
                    cameraDataSource.resumePreview(cameraId);
                }
            } catch (Exception e) {
                LogUtil.e(TAG, "Exception resuming all previews", e);
            }
        });
    }

    /**
     * 检查是否所有相机都在录制
     */
    public boolean isAllCamerasRecording() {
        for (CameraInfo info : cameraInfoCache.values()) {
            if (!info.isRecording()) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * 更新相机状态并通知观察者
     */
    private void updateCameraState() {
        List<String> activeCameraIds = java.util.Arrays.asList(CameraConstants.CAMERA_IDS);
        CameraState state = new CameraState(new HashMap<>(cameraInfoCache), activeCameraIds);
        state.setAllCamerasRecording(isAllCamerasRecording());
        state.setSystemReady(true);
        
        _cameraState.postValue(state);
        _cameraInfoMap.postValue(new HashMap<>(cameraInfoCache));
    }
    
    /**
     * 释放资源
     */
    public void release() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
        cameraDataSource.release();
        LogUtil.d(TAG, "CameraRepository released");
    }
}
