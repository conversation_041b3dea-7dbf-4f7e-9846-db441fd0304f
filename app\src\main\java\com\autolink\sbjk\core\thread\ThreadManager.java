package com.autolink.sbjk.core.thread;

import com.autolink.sbjk.common.util.LogUtil;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 线程管理器
 * 统一管理应用中的线程池资源
 */
public class ThreadManager {
    
    private static final String TAG = "ThreadManager";
    private static volatile ThreadManager instance;
    
    // 线程池配置
    private static final int CAMERA_THREAD_POOL_SIZE = 4;  // 相机操作线程池大小
    private static final int IO_THREAD_POOL_SIZE = 2;      // IO操作线程池大小
    private static final int BACKGROUND_THREAD_POOL_SIZE = 2; // 后台任务线程池大小
    
    // 线程池实例
    private final ExecutorService cameraExecutor;
    private final ExecutorService ioExecutor;
    private final ExecutorService backgroundExecutor;
    private final ScheduledExecutorService scheduledExecutor;
    
    private ThreadManager() {
        // 创建相机操作线程池
        this.cameraExecutor = Executors.newFixedThreadPool(
            CAMERA_THREAD_POOL_SIZE, 
            new NamedThreadFactory("Camera")
        );
        
        // 创建IO操作线程池
        this.ioExecutor = Executors.newFixedThreadPool(
            IO_THREAD_POOL_SIZE, 
            new NamedThreadFactory("IO")
        );
        
        // 创建后台任务线程池
        this.backgroundExecutor = Executors.newFixedThreadPool(
            BACKGROUND_THREAD_POOL_SIZE, 
            new NamedThreadFactory("Background")
        );
        
        // 创建定时任务线程池
        this.scheduledExecutor = Executors.newScheduledThreadPool(
            2, 
            new NamedThreadFactory("Scheduled")
        );
        
        LogUtil.d(TAG, "ThreadManager initialized with " +
                  "Camera=" + CAMERA_THREAD_POOL_SIZE +
                  ", IO=" + IO_THREAD_POOL_SIZE +
                  ", Background=" + BACKGROUND_THREAD_POOL_SIZE +
                  ", Scheduled=2 threads");
    }
    
    public static ThreadManager getInstance() {
        if (instance == null) {
            synchronized (ThreadManager.class) {
                if (instance == null) {
                    instance = new ThreadManager();
                }
            }
        }
        return instance;
    }
    
    /**
     * 获取相机操作线程池
     * 用于相机初始化、录制开始/停止等操作
     */
    public ExecutorService getCameraExecutor() {
        return cameraExecutor;
    }
    
    /**
     * 获取IO操作线程池
     * 用于文件读写、网络请求等IO密集型操作
     */
    public ExecutorService getIOExecutor() {
        return ioExecutor;
    }
    
    /**
     * 获取后台任务线程池
     * 用于数据处理、状态更新等后台任务
     */
    public ExecutorService getBackgroundExecutor() {
        return backgroundExecutor;
    }
    
    /**
     * 获取定时任务线程池
     * 用于定时监控、状态检查等定时任务
     */
    public ScheduledExecutorService getScheduledExecutor() {
        return scheduledExecutor;
    }
    
    /**
     * 执行相机操作任务
     */
    public void executeCameraTask(Runnable task) {
        if (task != null && !cameraExecutor.isShutdown()) {
            cameraExecutor.execute(wrapTask(task, "Camera"));
        }
    }
    
    /**
     * 执行IO操作任务
     */
    public void executeIOTask(Runnable task) {
        if (task != null && !ioExecutor.isShutdown()) {
            ioExecutor.execute(wrapTask(task, "IO"));
        }
    }
    
    /**
     * 执行后台任务
     */
    public void executeBackgroundTask(Runnable task) {
        if (task != null && !backgroundExecutor.isShutdown()) {
            backgroundExecutor.execute(wrapTask(task, "Background"));
        }
    }

    /**
     * 提交后台任务并返回Future
     */
    public Future<?> submitBackgroundTask(Runnable task) {
        if (task != null && !backgroundExecutor.isShutdown()) {
            return backgroundExecutor.submit(wrapTask(task, "Background"));
        }
        return null;
    }
    
    /**
     * 执行延迟任务
     */
    public void executeDelayedTask(Runnable task, long delay, TimeUnit unit) {
        if (task != null && !scheduledExecutor.isShutdown()) {
            scheduledExecutor.schedule(wrapTask(task, "Scheduled"), delay, unit);
        }
    }
    
    /**
     * 执行定时重复任务
     */
    public void executePeriodicTask(Runnable task, long initialDelay, long period, TimeUnit unit) {
        if (task != null && !scheduledExecutor.isShutdown()) {
            scheduledExecutor.scheduleAtFixedRate(wrapTask(task, "Periodic"), initialDelay, period, unit);
        }
    }
    
    /**
     * 包装任务，添加异常处理和日志
     */
    private Runnable wrapTask(Runnable task, String taskType) {
        return () -> {
            try {
                long startTime = System.currentTimeMillis();
                task.run();
                long duration = System.currentTimeMillis() - startTime;
                
                if (duration > 1000) { // 记录耗时超过1秒的任务
                    LogUtil.d(TAG, taskType + " task completed in " + duration + "ms");
                }
                
            } catch (Exception e) {
                LogUtil.e(TAG, "Exception in " + taskType + " task", e);
            }
        };
    }
    
    /**
     * 获取线程池状态信息
     */
    public ThreadPoolStatus getThreadPoolStatus() {
        return new ThreadPoolStatus(
            !cameraExecutor.isShutdown(),
            !ioExecutor.isShutdown(),
            !backgroundExecutor.isShutdown(),
            !scheduledExecutor.isShutdown()
        );
    }
    
    /**
     * 优雅关闭所有线程池
     */
    public void shutdown() {
        LogUtil.i(TAG, "Shutting down ThreadManager");
        
        shutdownExecutor(cameraExecutor, "Camera");
        shutdownExecutor(ioExecutor, "IO");
        shutdownExecutor(backgroundExecutor, "Background");
        shutdownExecutor(scheduledExecutor, "Scheduled");
        
        LogUtil.d(TAG, "ThreadManager shutdown completed");
    }
    
    /**
     * 强制关闭所有线程池
     */
    public void shutdownNow() {
        LogUtil.w(TAG, "Force shutting down ThreadManager");
        
        cameraExecutor.shutdownNow();
        ioExecutor.shutdownNow();
        backgroundExecutor.shutdownNow();
        scheduledExecutor.shutdownNow();
        
        LogUtil.d(TAG, "ThreadManager force shutdown completed");
    }
    
    /**
     * 优雅关闭单个线程池
     */
    private void shutdownExecutor(ExecutorService executor, String name) {
        try {
            executor.shutdown();
            
            if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                LogUtil.w(TAG, name + " executor did not terminate gracefully, forcing shutdown");
                executor.shutdownNow();
                
                if (!executor.awaitTermination(2, TimeUnit.SECONDS)) {
                    LogUtil.e(TAG, name + " executor did not terminate after force shutdown");
                }
            }
            
            LogUtil.d(TAG, name + " executor shutdown completed");
            
        } catch (InterruptedException e) {
            LogUtil.w(TAG, name + " executor shutdown interrupted", e);
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 自定义线程工厂
     */
    private static class NamedThreadFactory implements ThreadFactory {
        private final AtomicInteger threadNumber = new AtomicInteger(1);
        private final String namePrefix;
        
        public NamedThreadFactory(String namePrefix) {
            this.namePrefix = "SBJK-" + namePrefix + "-";
        }
        
        @Override
        public Thread newThread(Runnable r) {
            Thread thread = new Thread(r, namePrefix + threadNumber.getAndIncrement());
            thread.setDaemon(false);
            thread.setPriority(Thread.NORM_PRIORITY);
            return thread;
        }
    }
    
    /**
     * 线程池状态数据类
     */
    public static class ThreadPoolStatus {
        public final boolean cameraExecutorActive;
        public final boolean ioExecutorActive;
        public final boolean backgroundExecutorActive;
        public final boolean scheduledExecutorActive;
        
        public ThreadPoolStatus(boolean cameraExecutorActive, boolean ioExecutorActive, 
                               boolean backgroundExecutorActive, boolean scheduledExecutorActive) {
            this.cameraExecutorActive = cameraExecutorActive;
            this.ioExecutorActive = ioExecutorActive;
            this.backgroundExecutorActive = backgroundExecutorActive;
            this.scheduledExecutorActive = scheduledExecutorActive;
        }
        
        public boolean isAllActive() {
            return cameraExecutorActive && ioExecutorActive && 
                   backgroundExecutorActive && scheduledExecutorActive;
        }
        
        @Override
        public String toString() {
            return "ThreadPoolStatus{" +
                   "camera=" + cameraExecutorActive +
                   ", io=" + ioExecutorActive +
                   ", background=" + backgroundExecutorActive +
                   ", scheduled=" + scheduledExecutorActive +
                   '}';
        }
    }
}
