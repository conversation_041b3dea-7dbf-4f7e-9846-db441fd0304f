package com.autolink.sbjk.vehicle;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.widget.Toast;

import com.autolink.sbjk.common.util.LogUtil;
import com.autolink.sbjk.viewmodel.MainViewModel;
import com.autolink.sbjk.vehicle.VehiclePropertyKey;

/**
 * 智能车辆录制控制器
 * 负责根据车辆状态自动控制录制的启动和停止
 */
public class VehicleRecordingController implements VehicleManager.VehicleDataListener {
    
    private static final String TAG = "VehicleRecordingController";
    
    // === 依赖注入 ===
    private final Context context;
    private final MainViewModel mainViewModel;
    private VehicleManager vehicleManager;
    
    // === 状态管理 ===
    private boolean isSentryAutoModeEnabled = false;   // 哨兵自动启动开关
    private boolean isCurrentlyRecording = false;      // 当前录制状态
    private RecordingStartMode currentStartMode = RecordingStartMode.NONE; // 启动方式
    private int currentKeyState = -1;
    private int currentGearPosition = -1;
    
    // === 启动方式枚举 ===
    public enum RecordingStartMode {
        NONE,           // 未录制
        MANUAL,         // 手动启动
        AUTO_SENTRY     // 哨兵自动启动
    }
    
    // === 回调接口 ===
    public interface ConditionCallback {
        void onConditionMet();                         // 条件满足，录制已启动
        void onConditionNotMet(String reason);        // 条件不满足
    }
    
    // === 构造函数 ===
    public VehicleRecordingController(Context context, MainViewModel mainViewModel) {
        this.context = context.getApplicationContext();
        this.mainViewModel = mainViewModel;
        LogUtil.i(TAG, "VehicleRecordingController created");
    }
    
    // === 核心方法 ===
    
    /**
     * 手动启动录制 - 带条件检查
     */
    public void startManualRecording(ConditionCallback callback) {
        LogUtil.i(TAG, "Manual start requested");
        
        if (isRecordingConditionMet()) {
            // 条件满足，启动录制
            currentStartMode = RecordingStartMode.MANUAL;
            isCurrentlyRecording = true;
            
            // 调用原始启动方法（不侵入）
            mainViewModel.startAllCamerasRecording();
            
            LogUtil.i(TAG, "Manual recording started successfully");
            callback.onConditionMet();
            
        } else {
            // 条件不满足，提示用户
            String reason = getConditionFailureReason();
            LogUtil.w(TAG, "Manual start failed: " + reason);
            callback.onConditionNotMet(reason);
        }
    }
    
    /**
     * 手动停止录制（仅在哨兵模式关闭时可用）
     */
    public void stopManualRecording() {
        LogUtil.i(TAG, "Manual stop requested");

        if (isCurrentlyRecording && currentStartMode == RecordingStartMode.MANUAL) {
            currentStartMode = RecordingStartMode.NONE;
            isCurrentlyRecording = false;

            // 调用原始停止方法（不侵入）
            mainViewModel.stopAllCamerasRecording();

            LogUtil.i(TAG, "Manual recording stopped");
        } else if (currentStartMode == RecordingStartMode.AUTO_SENTRY) {
            LogUtil.w(TAG, "Cannot manually stop auto-started recording, please disable sentry mode first");
        }
    }
    
    /**
     * 设置哨兵自动模式
     */
    public void setSentryAutoMode(boolean enabled) {
        LogUtil.i(TAG, "Sentry auto mode: " + enabled);
        
        isSentryAutoModeEnabled = enabled;
        
        if (enabled) {
            // 开启哨兵模式，立即检查是否可以自动启动
            checkSentryAutoStart();
        } else {
            // 关闭哨兵模式，如果是自动启动的录制则停止
            if (currentStartMode == RecordingStartMode.AUTO_SENTRY) {
                stopAutoRecording("哨兵模式已关闭");
            }
        }
    }
    
    /**
     * 获取当前状态
     */
    public boolean isRecording() {
        return isCurrentlyRecording;
    }
    
    public RecordingStartMode getCurrentStartMode() {
        return currentStartMode;
    }
    
    public boolean isSentryAutoModeEnabled() {
        return isSentryAutoModeEnabled;
    }
    
    // === 车辆状态监听 ===
    @Override
    public void onVehiclePropertyChanged(VehiclePropertyKey key, Object value, boolean selfGet) {
        boolean stateChanged = false;

        if (key == VehiclePropertyKey.KeyVehiclePropertyKeySts) {
            int newKeyState = (Integer) value;
            if (newKeyState != currentKeyState) {
                currentKeyState = newKeyState;
                stateChanged = true;
                LogUtil.d(TAG, "Key state changed: " + vehicleManager.getKeyStateName(newKeyState));
            }
        } else if (key == VehiclePropertyKey.KeyVehiclePropertyGearPosition) {
            int newGearPosition = (Integer) value;
            if (newGearPosition != currentGearPosition) {
                currentGearPosition = newGearPosition;
                stateChanged = true;
                LogUtil.d(TAG, "Gear position changed: " + vehicleManager.getGearPositionName(newGearPosition));
            }
        }

        // 状态变化时检查自动控制逻辑
        if (stateChanged) {
            checkAutoControlLogic();
        }
    }

    // === 私有逻辑方法 ===

    /**
     * 检查录制条件是否满足
     */
    private boolean isRecordingConditionMet() {
        // 条件：钥匙ACC以上 且在P档
        boolean keyOk = currentKeyState >= VehicleManager.KEY_STATE_ACC;
        boolean gearOk = currentGearPosition == VehicleManager.GEAR_POSITION_P;

        LogUtil.i(TAG, "Condition check - Key: " + keyOk + " (" + currentKeyState + "), Gear: " + gearOk + " (" + currentGearPosition + ")");

        return keyOk && gearOk;
    }

    /**
     * 获取条件不满足的原因
     */
    private String getConditionFailureReason() {
        if (currentKeyState < VehicleManager.KEY_STATE_ACC) {
            return "电源状态不满足录制条件";
        }
        if (currentGearPosition != VehicleManager.GEAR_POSITION_P) {
            return "挡位状态不满足录制条件";
        }
        return "车辆状态不满足录制条件";
    }

    /**
     * 检查哨兵自动启动
     */
    private void checkSentryAutoStart() {
        if (!isSentryAutoModeEnabled || isCurrentlyRecording) {
            return;
        }

        if (isRecordingConditionMet()) {
            // 条件满足，自动启动录制
            currentStartMode = RecordingStartMode.AUTO_SENTRY;
            isCurrentlyRecording = true;

            // 调用原始启动方法（不侵入）
            mainViewModel.startAllCamerasRecording();

            LogUtil.i(TAG, "Sentry auto recording started");
            showToast("哨兵模式自动启动录制");

        } else {
            // 条件不满足，提示用户
            String reason = getConditionFailureReason();
            LogUtil.w(TAG, "Sentry auto start failed: " + reason);
            showToast(reason);
        }
    }

    /**
     * 检查自动停止（核心逻辑）
     */
    private void checkAutoStop() {
        // 只有在录制中且条件不满足时才自动停止
        if (isCurrentlyRecording && !isRecordingConditionMet()) {

            String stopReason = "条件不满足，自动停止录制";
            LogUtil.i(TAG, "Auto stopping recording: " + getConditionFailureReason());

            // 自动停止录制
            stopAutoRecording(stopReason);
        }
    }

    /**
     * 自动停止录制
     */
    private void stopAutoRecording(String reason) {
        if (isCurrentlyRecording) {
            currentStartMode = RecordingStartMode.NONE;
            isCurrentlyRecording = false;

            // 调用原始停止方法（不侵入）
            mainViewModel.stopAllCamerasRecording();

            LogUtil.i(TAG, "Auto recording stopped: " + reason);
            showToast(reason);
        }
    }

    /**
     * 综合自动控制逻辑检查
     */
    private void checkAutoControlLogic() {
        // 1. 优先检查自动停止（任何录制状态下条件不满足都要停止）
        checkAutoStop();

        // 2. 检查哨兵自动启动（只有在未录制且哨兵模式开启时）
        if (isSentryAutoModeEnabled && !isCurrentlyRecording) {
            checkSentryAutoStart();
        }
    }

    /**
     * 初始化车辆服务
     */
    public void initialize() {
        try {
            vehicleManager = VehicleManager.getInstance(context);
            vehicleManager.addListener(this);

            LogUtil.i(TAG, "VehicleRecordingController initialized");

            // 延迟获取初始状态，等待服务连接
            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                try {
                    // 获取初始状态
                    currentKeyState = vehicleManager.getIntProperty(VehiclePropertyKey.KeyVehiclePropertyKeySts);
                    currentGearPosition = vehicleManager.getIntProperty(VehiclePropertyKey.KeyVehiclePropertyGearPosition);

                    LogUtil.i(TAG, "Initial state - Key: " + vehicleManager.getKeyStateName(currentKeyState) +
                             ", Gear: " + vehicleManager.getGearPositionName(currentGearPosition));
                } catch (Exception e) {
                    LogUtil.e(TAG, "Failed to get initial vehicle state", e);
                }
            }, 1000); // 延迟1秒等待服务连接

        } catch (Exception e) {
            LogUtil.e(TAG, "Failed to initialize vehicle service", e);
        }
    }

    /**
     * 清理资源
     */
    public void cleanup() {
        if (vehicleManager != null) {
            vehicleManager.removeListener(this);
        }
        LogUtil.i(TAG, "VehicleRecordingController cleaned up");
    }

    /**
     * 显示提示信息
     */
    private void showToast(String message) {
        // 在主线程显示Toast
        new Handler(Looper.getMainLooper()).post(() -> {
            Toast.makeText(context, message, Toast.LENGTH_SHORT).show();
        });
    }


}
