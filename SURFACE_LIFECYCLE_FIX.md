# SurfaceView预览后台切换显示问题修复方案

## 问题描述
当应用从前台切换到后台（onPause），然后再回到前台（onResume）时，4个摄像头预览窗口中的某个或某些SurfaceView会随机性地变成黑屏或完全消失，无法显示预览内容。

## 根本原因分析

### 1. 生命周期管理缺失
- MainActivity在onPause/onResume时没有对SurfaceView进行特殊处理
- 系统可能在后台回收Surface资源，但应用没有主动管理

### 2. Surface有效性未检查
- 没有验证Surface.isValid()状态
- 无效的Surface被错误地设置到编码器中

### 3. 相机资源后台处理不当
- CameraDevice在后台可能被系统回收
- 没有预览暂停/恢复机制

### 4. 并发竞争条件
- 4个SurfaceView同时初始化时存在竞争
- 异步Surface操作缺乏时序控制

## 解决方案

### 1. 增强Activity生命周期管理

**MainActivity.java 修改：**
- onResume()中添加Surface预览恢复逻辑
- onPause()中添加Surface预览暂停逻辑
- 延迟500ms执行恢复，确保Surface准备就绪

```java
@Override
protected void onResume() {
    super.onResume();
    // ... 原有逻辑
    
    // 恢复Surface预览 - 延迟执行确保Surface已准备好
    if (mainViewModel != null) {
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            restoreSurfacePreviews();
        }, 500);
    }
}

@Override
protected void onPause() {
    super.onPause();
    // ... 原有逻辑
    
    // 暂停Surface预览但保持录制
    if (mainViewModel != null) {
        pauseSurfacePreviews();
    }
}
```

### 2. Surface状态监控和恢复

**新增方法：**
- `restoreSurfacePreviews()`: 恢复所有Surface预览
- `restoreSingleSurfacePreview()`: 恢复单个Surface预览
- `pauseSurfacePreviews()`: 暂停所有Surface预览

**关键特性：**
- Surface有效性检查：`surface.isValid()`
- 逐个恢复每个摄像头的预览
- 详细的日志记录用于调试

### 3. 增强SurfaceHolder.Callback处理

**改进点：**
- surfaceCreated/surfaceChanged中添加Surface有效性验证
- surfaceDestroyed中增加更详细的状态检查
- 区分配置变化和正常销毁

```java
@Override
public void surfaceCreated(@NonNull SurfaceHolder holder) {
    Surface surface = holder.getSurface();
    if (surface != null && surface.isValid()) {
        mainViewModel.onPreviewSurfaceAvailable(cameraId, surface);
    } else {
        LogUtil.w(TAG, "Surface created but invalid for camera " + cameraId);
    }
}
```

### 4. 多层架构的预览管理

**新增方法链：**
```
MainActivity → MainViewModel → CameraRepository → CameraDataSource → CameraController → CameraYuvEncoder
```

**每层新增的方法：**
- `pauseAllPreviews()` / `resumeAllPreviews()`
- `pausePreview()` / `resumePreview()`

### 5. Surface重试机制

**CameraRepository中的改进：**
- Surface设置失败时自动重试（最多3次）
- 延迟1秒后重试，避免资源竞争
- 详细的错误日志和状态跟踪

```java
private void retrySetPreviewSurface(String cameraId, Surface surface, int retryCount) {
    if (retryCount > 0) {
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            // 重试逻辑
        }, 1000);
    }
}
```

### 6. 编码器层面的预览控制

**CameraYuvEncoder中的改进：**
- `pausePreview()`: 暂停预览但保持录制
- `resumePreview()`: 恢复预览
- Surface有效性检查增强

## 修改文件列表

1. **MainActivity.java**
   - 生命周期方法增强
   - Surface状态管理方法
   - SurfaceHolder.Callback改进

2. **MainViewModel.java**
   - Surface有效性检查
   - 预览暂停/恢复方法

3. **CameraRepository.java**
   - Surface重试机制
   - 批量预览管理

4. **CameraDataSource.java**
   - 预览控制接口

5. **CameraController.java**
   - 预览暂停/恢复实现

6. **CameraYuvEncoder.java**
   - Surface有效性检查增强
   - 预览控制逻辑

## 测试验证

创建了 `SurfaceLifecycleTest.java` 用于验证：
- Surface有效性检查
- 后台切换恢复机制
- 多Surface并发初始化
- 重试机制
- 配置变化处理

## 预期效果

1. **消除随机黑屏**：通过Surface有效性检查和重试机制
2. **稳定的后台切换**：主动管理预览暂停/恢复
3. **改善并发处理**：避免多Surface竞争条件
4. **增强错误恢复**：自动重试和详细日志

## 使用建议

1. **监控日志**：关注Surface相关的日志输出
2. **测试场景**：重点测试频繁的前后台切换
3. **性能观察**：确认预览恢复不影响录制性能
4. **边界情况**：测试低内存、多任务等极端情况
