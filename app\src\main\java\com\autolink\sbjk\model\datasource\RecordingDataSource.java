package com.autolink.sbjk.model.datasource;

import android.content.Context;

import com.autolink.sbjk.common.util.LogUtil;
import com.autolink.sbjk.model.entity.RecordingSession;
import com.autolink.sbjk.model.entity.VideoSegment;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 录制数据源
 * 管理录制会话和视频片段数据
 */
public class RecordingDataSource {
    
    private static final String TAG = "RecordingDataSource";
    
    private final Context context;
    private final Map<String, RecordingSession> activeSessions;
    private final Map<String, List<VideoSegment>> cameraSegments;
    
    public RecordingDataSource(Context context) {
        this.context = context.getApplicationContext();
        this.activeSessions = new ConcurrentHashMap<>();
        this.cameraSegments = new ConcurrentHashMap<>();
        
        LogUtil.d(TAG, "RecordingDataSource initialized");
    }
    
    /**
     * 创建录制会话
     */
    public RecordingSession createSession(String cameraId, String basePath) {
        try {
            String sessionId = generateSessionId(cameraId);
            RecordingSession session = new RecordingSession(sessionId, cameraId);
            session.setBasePath(basePath);
            
            activeSessions.put(cameraId, session);
            
            // 确保相机片段列表存在
            if (!cameraSegments.containsKey(cameraId)) {
                cameraSegments.put(cameraId, new ArrayList<>());
            }
            
            LogUtil.i(TAG, "Recording session created: " + sessionId + " for camera " + cameraId);
            return session;
            
        } catch (Exception e) {
            LogUtil.e(TAG, "Failed to create recording session for camera " + cameraId, e);
            return null;
        }
    }
    
    /**
     * 结束录制会话
     */
    public boolean endSession(String cameraId) {
        try {
            RecordingSession session = activeSessions.get(cameraId);
            if (session != null) {
                session.endSession();
                activeSessions.remove(cameraId);
                LogUtil.i(TAG, "Recording session ended for camera " + cameraId);
                return true;
            }
            return false;
        } catch (Exception e) {
            LogUtil.e(TAG, "Failed to end recording session for camera " + cameraId, e);
            return false;
        }
    }
    
    /**
     * 添加视频片段
     */
    public VideoSegment addSegment(String cameraId, String filePath, long duration) {
        try {
            RecordingSession session = activeSessions.get(cameraId);
            if (session == null) {
                LogUtil.w(TAG, "No active session for camera " + cameraId);
                return null;
            }
            
            List<VideoSegment> segments = cameraSegments.get(cameraId);
            if (segments == null) {
                segments = new ArrayList<>();
                cameraSegments.put(cameraId, segments);
            }
            
            String segmentId = generateSegmentId(cameraId, segments.size());
            VideoSegment segment = new VideoSegment(segmentId, session.getSessionId(), 
                                                  cameraId, filePath, segments.size() + 1);
            
            if (duration > 0) {
                segment.setDuration(duration);
                segment.completeSegment();
            }
            
            segments.add(segment);
            session.addSegment(segment);
            
            LogUtil.d(TAG, "Video segment added: " + segmentId + " for camera " + cameraId);
            return segment;
            
        } catch (Exception e) {
            LogUtil.e(TAG, "Failed to add video segment for camera " + cameraId, e);
            return null;
        }
    }
    
    /**
     * 获取录制会话
     */
    public RecordingSession getSession(String cameraId) {
        return activeSessions.get(cameraId);
    }
    
    /**
     * 获取所有活跃会话
     */
    public List<RecordingSession> getActiveSessions() {
        return new ArrayList<>(activeSessions.values());
    }
    
    /**
     * 获取相机的视频片段
     */
    public List<VideoSegment> getSegments(String cameraId) {
        List<VideoSegment> segments = cameraSegments.get(cameraId);
        return segments != null ? new ArrayList<>(segments) : new ArrayList<>();
    }
    
    /**
     * 获取总录制时长
     */
    public long getTotalDuration() {
        long totalDuration = 0;
        for (List<VideoSegment> segments : cameraSegments.values()) {
            for (VideoSegment segment : segments) {
                totalDuration += segment.getDuration();
            }
        }
        return totalDuration;
    }
    
    /**
     * 获取相机录制时长
     */
    public long getCameraDuration(String cameraId) {
        List<VideoSegment> segments = cameraSegments.get(cameraId);
        if (segments == null) {
            return 0;
        }
        
        long duration = 0;
        for (VideoSegment segment : segments) {
            duration += segment.getDuration();
        }
        return duration;
    }
    
    /**
     * 获取总存储使用量
     */
    public long getTotalStorageUsage() {
        long totalSize = 0;
        for (List<VideoSegment> segments : cameraSegments.values()) {
            for (VideoSegment segment : segments) {
                totalSize += segment.getFileSize();
            }
        }
        return totalSize;
    }
    
    /**
     * 获取可用存储空间
     */
    public long getAvailableSpace() {
        try {
            File externalDir = context.getExternalFilesDir(null);
            if (externalDir != null) {
                return externalDir.getFreeSpace();
            }
            return 0;
        } catch (Exception e) {
            LogUtil.e(TAG, "Failed to get available space", e);
            return 0;
        }
    }
    
    /**
     * 清理过期录制数据
     */
    public int cleanupExpired(long maxAgeMs) {
        int cleanedCount = 0;
        long currentTime = System.currentTimeMillis();
        
        try {
            for (Map.Entry<String, List<VideoSegment>> entry : cameraSegments.entrySet()) {
                List<VideoSegment> segments = entry.getValue();
                List<VideoSegment> toRemove = new ArrayList<>();
                
                for (VideoSegment segment : segments) {
                    if (currentTime - segment.getStartTime() > maxAgeMs) {
                        // 删除文件
                        if (segment.deleteFile()) {
                            toRemove.add(segment);
                            cleanedCount++;
                        }
                    }
                }
                
                segments.removeAll(toRemove);
            }
            
            LogUtil.i(TAG, "Cleaned up " + cleanedCount + " expired segments");
            
        } catch (Exception e) {
            LogUtil.e(TAG, "Failed to cleanup expired recordings", e);
        }
        
        return cleanedCount;
    }
    
    /**
     * 生成会话ID
     */
    private String generateSessionId(String cameraId) {
        return "session_" + cameraId + "_" + System.currentTimeMillis();
    }
    
    /**
     * 生成片段ID
     */
    private String generateSegmentId(String cameraId, int segmentIndex) {
        return "segment_" + cameraId + "_" + segmentIndex + "_" + System.currentTimeMillis();
    }
    
    /**
     * 释放资源
     */
    public void release() {
        try {
            // 结束所有活跃会话
            for (RecordingSession session : activeSessions.values()) {
                session.endSession();
            }
            
            activeSessions.clear();
            cameraSegments.clear();
            
            LogUtil.d(TAG, "RecordingDataSource released");
            
        } catch (Exception e) {
            LogUtil.e(TAG, "Exception releasing RecordingDataSource", e);
        }
    }
}
