plugins {
    id 'com.android.application'
}

android {
    namespace 'com.autolink.sbjk'
    compileSdk 34

    defaultConfig {
        applicationId "com.autolink.sbjk"
        minSdk 30
        targetSdk 34
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    // 启用AIDL支持
    buildFeatures {
        aidl true
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    // 基础库
    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation 'com.google.android.material:material:1.12.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.2.1'
    implementation 'androidx.cardview:cardview:1.0.0'
    
    // Camera2相关依赖
    implementation 'androidx.camera:camera-core:1.3.2'
    implementation 'androidx.camera:camera-camera2:1.3.2'
    implementation 'androidx.camera:camera-lifecycle:1.3.2'
    implementation 'androidx.camera:camera-view:1.3.2'
    
    // 媒体相关
    implementation 'androidx.media:media:1.7.0'
    
    // 测试库
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.2.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.6.1'
}