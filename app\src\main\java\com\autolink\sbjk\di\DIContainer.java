package com.autolink.sbjk.di;

import android.content.Context;

import com.autolink.sbjk.common.util.LogUtil;
import com.autolink.sbjk.model.repository.CameraRepository;
import com.autolink.sbjk.model.repository.RecordingRepository;
import com.autolink.sbjk.model.repository.StorageRepository;
import com.autolink.sbjk.viewmodel.MainViewModel;
import com.autolink.sbjk.viewmodel.CameraPreviewViewModel;
import com.autolink.sbjk.vehicle.VehicleRecordingController;

/**
 * 简化版依赖注入容器
 * 提供Repository和ViewModel的依赖注入支持
 */
public class DIContainer {
    
    private static final String TAG = "DIContainer";
    
    // 单例实例缓存
    private static volatile CameraRepository cameraRepositoryInstance;
    private static volatile RecordingRepository recordingRepositoryInstance;
    private static volatile StorageRepository storageRepositoryInstance;
    
    /**
     * 提供CameraRepository实例
     */
    public static CameraRepository provideCameraRepository(Context context) {
        if (cameraRepositoryInstance == null) {
            synchronized (DIContainer.class) {
                if (cameraRepositoryInstance == null) {
                    cameraRepositoryInstance = CameraRepository.getInstance(context);
                    LogUtil.d(TAG, "CameraRepository instance created");
                }
            }
        }
        return cameraRepositoryInstance;
    }
    
    /**
     * 提供RecordingRepository实例
     */
    public static RecordingRepository provideRecordingRepository(Context context) {
        if (recordingRepositoryInstance == null) {
            synchronized (DIContainer.class) {
                if (recordingRepositoryInstance == null) {
                    recordingRepositoryInstance = RecordingRepository.getInstance(context);
                    LogUtil.d(TAG, "RecordingRepository instance created");
                }
            }
        }
        return recordingRepositoryInstance;
    }

    /**
     * 提供StorageRepository实例
     */
    public static StorageRepository provideStorageRepository(Context context) {
        if (storageRepositoryInstance == null) {
            synchronized (DIContainer.class) {
                if (storageRepositoryInstance == null) {
                    storageRepositoryInstance = StorageRepository.getInstance(context);
                    LogUtil.d(TAG, "StorageRepository instance created");
                }
            }
        }
        return storageRepositoryInstance;
    }
    
    /**
     * 提供MainViewModel实例（使用依赖注入构造函数）
     */
    public static MainViewModel provideMainViewModel(Context context) {
        CameraRepository cameraRepository = provideCameraRepository(context);
        RecordingRepository recordingRepository = provideRecordingRepository(context);
        StorageRepository storageRepository = provideStorageRepository(context);

        MainViewModel viewModel = new MainViewModel(cameraRepository, recordingRepository, storageRepository);
        LogUtil.d(TAG, "MainViewModel instance created with dependency injection");
        return viewModel;
    }
    
    /**
     * 提供MainViewModel实例（直接传入Repository）
     * 主要用于单元测试
     */
    public static MainViewModel provideMainViewModel(CameraRepository cameraRepository,
                                                   RecordingRepository recordingRepository,
                                                   StorageRepository storageRepository) {
        if (cameraRepository == null || recordingRepository == null || storageRepository == null) {
            throw new IllegalArgumentException("Repository instances cannot be null");
        }

        MainViewModel viewModel = new MainViewModel(cameraRepository, recordingRepository, storageRepository);
        LogUtil.d(TAG, "MainViewModel instance created with injected repositories");
        return viewModel;
    }

    /**
     * 提供CameraPreviewViewModel实例（使用依赖注入构造函数）
     */
    public static CameraPreviewViewModel provideCameraPreviewViewModel(Context context) {
        CameraRepository cameraRepository = provideCameraRepository(context);
        RecordingRepository recordingRepository = provideRecordingRepository(context);

        CameraPreviewViewModel viewModel = new CameraPreviewViewModel(cameraRepository, recordingRepository);
        LogUtil.d(TAG, "CameraPreviewViewModel instance created with dependency injection");
        return viewModel;
    }

    /**
     * 提供CameraPreviewViewModel实例（直接传入Repository）
     * 主要用于单元测试
     */
    public static CameraPreviewViewModel provideCameraPreviewViewModel(CameraRepository cameraRepository,
                                                                      RecordingRepository recordingRepository) {
        if (cameraRepository == null || recordingRepository == null) {
            throw new IllegalArgumentException("Repository instances cannot be null");
        }

        CameraPreviewViewModel viewModel = new CameraPreviewViewModel(cameraRepository, recordingRepository);
        LogUtil.d(TAG, "CameraPreviewViewModel instance created with injected repositories");
        return viewModel;
    }

    /**
     * 提供VehicleRecordingController实例
     */
    public static VehicleRecordingController provideVehicleRecordingController(Context context) {
        MainViewModel mainViewModel = provideMainViewModel(context);
        VehicleRecordingController controller = new VehicleRecordingController(context, mainViewModel);
        LogUtil.d(TAG, "VehicleRecordingController instance created with dependency injection");
        return controller;
    }

    /**
     * 清理所有缓存的实例（主要用于测试）
     */
    public static void clearInstances() {
        synchronized (DIContainer.class) {
            if (cameraRepositoryInstance != null) {
                cameraRepositoryInstance.release();
                cameraRepositoryInstance = null;
            }
            
            if (recordingRepositoryInstance != null) {
                recordingRepositoryInstance.release();
                recordingRepositoryInstance = null;
            }

            if (storageRepositoryInstance != null) {
                storageRepositoryInstance.release();
                storageRepositoryInstance = null;
            }

            LogUtil.d(TAG, "All instances cleared");
        }
    }
    
    /**
     * 检查依赖是否已初始化
     */
    public static boolean isDependenciesInitialized() {
        return cameraRepositoryInstance != null && recordingRepositoryInstance != null && storageRepositoryInstance != null;
    }
    
    /**
     * 获取依赖状态信息（用于调试）
     */
    public static String getDependencyStatus() {
        return "DIContainer Status: " +
               "CameraRepository=" + (cameraRepositoryInstance != null ? "initialized" : "null") +
               ", RecordingRepository=" + (recordingRepositoryInstance != null ? "initialized" : "null") +
               ", StorageRepository=" + (storageRepositoryInstance != null ? "initialized" : "null");
    }
    
    // 私有构造函数，防止实例化
    private DIContainer() {
        throw new AssertionError("DIContainer should not be instantiated");
    }
}
