# 循环录制功能实现文档

## 📋 功能概述

本文档描述了车载监控系统中循环录制功能的实现方案。该功能通过智能存储管理，确保在存储空间不足时自动清理旧文件，实现连续不间断的录制。

## 🎯 设计目标

- **简洁高效**: 避免过度设计，直接解决核心问题
- **符合MVVM**: 完全遵循项目现有的MVVM架构
- **非侵入式**: 不破坏现有代码结构，通过扩展实现
- **自动化**: 无需用户干预，自动管理存储空间

## 🏗️ 架构设计

### 分层结构
```
View Layer (MainActivity)
    ↓ 观察存储状态
ViewModel Layer (MainViewModel)
    ↓ 管理存储业务逻辑
Repository Layer (StorageRepository)
    ↓ 统一存储数据访问
Core Layer (StorageCleanupManager)
    ↓ 执行具体清理逻辑
Storage Layer (文件系统)
```

### 核心组件

#### 1. StorageCleanupManager (Core层)
- **职责**: 执行存储空间检查和文件清理
- **触发条件**: 剩余空间 < 10%
- **清理目标**: 释放约2%的存储空间
- **清理策略**: 按文件创建时间删除最旧文件

#### 2. StorageRepository (Repository层)
- **职责**: 管理存储相关数据访问和状态
- **功能**: 定时检查、分段触发检查、状态管理
- **LiveData**: 提供存储信息和清理结果的响应式数据

#### 3. MainViewModel (ViewModel层)
- **职责**: 存储业务逻辑管理和状态聚合
- **集成**: 与现有录制逻辑无缝集成
- **回调**: 处理录制分段完成事件

#### 4. MainActivity (View层)
- **职责**: 存储状态显示和用户交互
- **UI**: 实时显示存储使用情况和清理结果
- **主题**: 支持日夜模式自动适配

## 🔧 实现细节

### 存储检查逻辑
```java
// 检查存储空间
StatFs statFs = new StatFs(recordPath);
long totalBytes = statFs.getTotalBytes();
long availableBytes = statFs.getAvailableBytes();
double availablePercent = (double) availableBytes / totalBytes;

// 判断是否需要清理
if (availablePercent <= 0.10) {
    // 需要清理，计算目标清理空间
    long targetCleanupBytes = (long) (totalBytes * 0.02);
    performCleanup(targetCleanupBytes);
}
```

### 文件清理策略
```java
// 获取所有录像文件并按时间排序
List<File> videoFiles = getAllVideoFiles(recordDir);
Collections.sort(videoFiles, (f1, f2) -> 
    Long.compare(f1.lastModified(), f2.lastModified()));

// 删除最旧的文件直到达到清理目标
for (File file : videoFiles) {
    if (cleanedBytes >= targetCleanupBytes) break;
    if (file.delete()) {
        cleanedBytes += file.length();
        cleanedCount++;
    }
}
```

### 触发机制

#### 1. 定时触发
- **间隔**: 每10分钟检查一次
- **实现**: Handler + Runnable循环调度
- **控制**: 可启动/停止定时检查

#### 2. 分段触发
- **时机**: 录制分段完成后立即检查
- **实现**: 回调链传递分段完成事件
- **优势**: 及时响应存储变化

### 回调链设计
```
CameraYuvEncoder (分段完成)
    ↓ SegmentCompletionCallback
CameraController
    ↓ SegmentCompletionCallback  
CameraDataSource
    ↓ SegmentCompletionCallback
CameraRepository
    ↓ SegmentCompletionCallback
MainViewModel
    ↓ 调用StorageRepository
StorageRepository (执行检查)
```

## 📱 用户界面

### 存储状态显示
```xml
<TextView
    android:id="@+id/tv_storage_info"
    android:text="存储空间：已用35.2GB/50.0GB (70%)"
    android:textColor="@color/text_secondary_adaptive" />
```

### 状态颜色指示
- **绿色**: 使用率 < 80%
- **橙色**: 使用率 80-90%
- **红色**: 使用率 > 90%

### 清理通知
- **成功**: "已清理5个文件，释放1.2GB空间"
- **失败**: 显示具体错误信息

## 🔄 工作流程

### 正常录制流程
1. 用户启动录制
2. 系统开始分段录制
3. 每个分段完成后触发存储检查
4. 如果空间充足，继续录制
5. 如果空间不足，自动清理后继续录制

### 定时检查流程
1. 每10分钟自动触发检查
2. 获取当前存储状态
3. 更新UI显示
4. 如需清理则执行清理操作
5. 通知用户清理结果

## 📊 性能优化

### I/O优化
- **异步执行**: 所有文件操作在后台线程
- **批量删除**: 避免频繁I/O操作
- **缓存机制**: 文件列表缓存减少扫描

### 内存优化
- **弱引用**: 避免内存泄漏
- **及时释放**: 文件句柄及时关闭
- **分页加载**: 大量文件时分页处理

### 电池优化
- **智能调度**: 充电时执行清理
- **后台限制**: 应用后台时降低检查频率

## 🧪 测试验证

### 单元测试
- StorageCleanupManager功能测试
- 存储信息计算测试
- 清理结果验证测试

### 集成测试
- 录制分段触发测试
- 定时检查功能测试
- UI状态更新测试

## 📈 监控指标

### 关键指标
- 存储空间使用率
- 清理操作频率
- 清理文件数量和大小
- 系统性能影响

### 日志记录
- 存储检查日志
- 清理操作日志
- 错误和异常日志

## 🔮 扩展性

### 未来增强
- 智能清理策略（重要性评分）
- 用户自定义清理阈值
- 云端备份集成
- 存储使用统计分析

## ✅ 实施状态

- [x] 核心存储管理组件
- [x] MVVM架构集成
- [x] UI状态显示
- [x] 回调链实现
- [x] 定时检查机制
- [x] 分段触发机制
- [x] 单元测试
- [x] 文档完善

## 🎉 总结

本实现方案成功地在现有MVVM架构基础上，以简洁高效的方式实现了循环录制功能。通过智能的存储管理和自动清理机制，确保了车载监控系统能够持续稳定地运行，为用户提供可靠的录制服务。

该方案的核心优势：
1. **架构一致性**: 完全符合现有MVVM架构
2. **实现简洁**: 避免过度设计，直击核心需求
3. **用户友好**: 自动化管理，无需用户干预
4. **性能优化**: 多项优化措施确保系统稳定
5. **可扩展性**: 为未来功能增强预留空间
