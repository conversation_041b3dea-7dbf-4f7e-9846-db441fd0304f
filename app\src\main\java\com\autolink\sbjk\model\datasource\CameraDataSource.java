package com.autolink.sbjk.model.datasource;

import android.content.Context;
import android.hardware.camera2.CameraManager;

import com.autolink.sbjk.common.constant.CameraConstants;
import com.autolink.sbjk.common.util.LogUtil;
import com.autolink.sbjk.core.camera.CameraController;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 相机数据源
 * 负责与底层相机硬件交互
 */
public class CameraDataSource {
    
    private static final String TAG = "CameraDataSource";
    
    private final Context context;
    private final CameraManager cameraManager;
    private final Map<String, CameraController> cameraControllers;

    // 分段完成回调
    private com.autolink.sbjk.common.callback.SegmentCompletionCallback segmentCompletionCallback;
    
    public CameraDataSource(Context context) {
        this.context = context.getApplicationContext();
        this.cameraManager = (CameraManager) context.getSystemService(Context.CAMERA_SERVICE);
        this.cameraControllers = new ConcurrentHashMap<>();
        
        initializeCameraControllers();
    }
    
    /**
     * 初始化相机控制器
     */
    private void initializeCameraControllers() {
        for (String cameraId : CameraConstants.CAMERA_IDS) {
            try {
                CameraController controller = new CameraController(context, cameraId);
                cameraControllers.put(cameraId, controller);
                LogUtil.d(TAG, "Camera controller initialized for " + cameraId);
            } catch (Exception e) {
                LogUtil.e(TAG, "Failed to initialize camera controller for " + cameraId, e);
            }
        }
    }
    
    /**
     * 检查相机是否可用
     */
    public boolean isCameraAvailable(String cameraId) {
        try {
            if (cameraManager == null) {
                return false;
            }
            
            String[] cameraIds = cameraManager.getCameraIdList();
            for (String id : cameraIds) {
                if (id.equals(cameraId)) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            LogUtil.e(TAG, "Error checking camera availability for " + cameraId, e);
            return false;
        }
    }
    
    /**
     * 开始录制
     */
    public boolean startRecording(String cameraId, String outputPath) {
        try {
            CameraController controller = cameraControllers.get(cameraId);
            if (controller == null) {
                LogUtil.e(TAG, "Camera controller not found for " + cameraId);
                return false;
            }
            
            if (!isCameraAvailable(cameraId)) {
                LogUtil.e(TAG, "Camera " + cameraId + " is not available");
                return false;
            }
            
            boolean success = controller.startRecording(outputPath);
            if (success) {
                LogUtil.i(TAG, "Recording started for camera " + cameraId + " to " + outputPath);
            } else {
                LogUtil.e(TAG, "Failed to start recording for camera " + cameraId);
            }
            
            return success;
            
        } catch (Exception e) {
            LogUtil.e(TAG, "Exception starting recording for camera " + cameraId, e);
            return false;
        }
    }
    
    /**
     * 停止录制
     */
    public boolean stopRecording(String cameraId) {
        try {
            CameraController controller = cameraControllers.get(cameraId);
            if (controller == null) {
                LogUtil.e(TAG, "Camera controller not found for " + cameraId);
                return false;
            }
            
            boolean success = controller.stopRecording();
            if (success) {
                LogUtil.i(TAG, "Recording stopped for camera " + cameraId);
            } else {
                LogUtil.e(TAG, "Failed to stop recording for camera " + cameraId);
            }
            
            return success;
            
        } catch (Exception e) {
            LogUtil.e(TAG, "Exception stopping recording for camera " + cameraId, e);
            return false;
        }
    }
    
    /**
     * 暂停录制
     */
    public boolean pauseRecording(String cameraId) {
        try {
            CameraController controller = cameraControllers.get(cameraId);
            if (controller == null) {
                return false;
            }
            
            return controller.pauseRecording();
        } catch (Exception e) {
            LogUtil.e(TAG, "Exception pausing recording for camera " + cameraId, e);
            return false;
        }
    }
    
    /**
     * 恢复录制
     */
    public boolean resumeRecording(String cameraId) {
        try {
            CameraController controller = cameraControllers.get(cameraId);
            if (controller == null) {
                return false;
            }
            
            return controller.resumeRecording();
        } catch (Exception e) {
            LogUtil.e(TAG, "Exception resuming recording for camera " + cameraId, e);
            return false;
        }
    }
    
    /**
     * 获取相机状态
     */
    public CameraConstants.CameraStatus getCameraStatus(String cameraId) {
        try {
            CameraController controller = cameraControllers.get(cameraId);
            if (controller == null) {
                return CameraConstants.CameraStatus.ERROR;
            }
            
            return controller.getStatus();
        } catch (Exception e) {
            LogUtil.e(TAG, "Exception getting camera status for " + cameraId, e);
            return CameraConstants.CameraStatus.ERROR;
        }
    }
    
    /**
     * 设置预览Surface
     */
    public boolean setPreviewSurface(String cameraId, android.view.Surface surface) {
        try {
            CameraController controller = cameraControllers.get(cameraId);
            if (controller == null) {
                return false;
            }
            
            return controller.setPreviewSurface(surface);
        } catch (Exception e) {
            LogUtil.e(TAG, "Exception setting preview surface for camera " + cameraId, e);
            return false;
        }
    }
    
    /**
     * 暂停预览但保持录制
     */
    public boolean pausePreview(String cameraId) {
        try {
            CameraController controller = cameraControllers.get(cameraId);
            if (controller == null) {
                return false;
            }

            return controller.pausePreview();
        } catch (Exception e) {
            LogUtil.e(TAG, "Exception pausing preview for camera " + cameraId, e);
            return false;
        }
    }

    /**
     * 恢复预览
     */
    public boolean resumePreview(String cameraId) {
        try {
            CameraController controller = cameraControllers.get(cameraId);
            if (controller == null) {
                return false;
            }

            return controller.resumePreview();
        } catch (Exception e) {
            LogUtil.e(TAG, "Exception resuming preview for camera " + cameraId, e);
            return false;
        }
    }

    /**
     * 获取相机控制器
     */
    public CameraController getCameraController(String cameraId) {
        return cameraControllers.get(cameraId);
    }

    /**
     * 设置分段完成回调
     */
    public void setSegmentCompletionCallback(com.autolink.sbjk.common.callback.SegmentCompletionCallback callback) {
        this.segmentCompletionCallback = callback;

        // 将回调传递给所有相机控制器
        for (CameraController controller : cameraControllers.values()) {
            if (controller != null) {
                controller.setSegmentCompletionCallback(callback);
            }
        }
    }
    
    /**
     * 释放资源
     */
    public void release() {
        try {
            for (CameraController controller : cameraControllers.values()) {
                if (controller != null) {
                    controller.release();
                }
            }
            cameraControllers.clear();
            LogUtil.d(TAG, "CameraDataSource released");
        } catch (Exception e) {
            LogUtil.e(TAG, "Exception releasing CameraDataSource", e);
        }
    }
}
