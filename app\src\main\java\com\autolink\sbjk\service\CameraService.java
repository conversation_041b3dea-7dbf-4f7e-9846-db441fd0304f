package com.autolink.sbjk.service;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.Binder;
import android.os.Build;
import android.os.IBinder;
import androidx.core.app.NotificationCompat;

import com.autolink.sbjk.MainActivity;
import com.autolink.sbjk.R;
import com.autolink.sbjk.common.constant.CameraConstants;
import com.autolink.sbjk.common.util.LogUtil;
import com.autolink.sbjk.model.repository.CameraRepository;
import com.autolink.sbjk.model.repository.RecordingRepository;
import com.autolink.sbjk.vehicle.VehicleRecordingController;
import com.autolink.sbjk.viewmodel.MainViewModel;
import com.autolink.sbjk.di.DIContainer;

/**
 * 相机服务 - 重构为使用Repository模式
 * 负责后台录制管理和系统通知
 */
public class CameraService extends Service {
    private static final String TAG = "CameraService";
    private static final String CHANNEL_ID = "CameraServiceChannel";
    private static final int NOTIFICATION_ID = 1;

    private final IBinder binder = new LocalBinder();
    private CameraRepository cameraRepository;
    private RecordingRepository recordingRepository;
    private VehicleRecordingController vehicleController;
    private boolean isRunning = false;

    public class LocalBinder extends Binder {
        public CameraService getService() {
            return CameraService.this;
        }
    }

    @Override
    public void onCreate() {
        super.onCreate();

        // 初始化Repository
        cameraRepository = CameraRepository.getInstance(this);
        recordingRepository = RecordingRepository.getInstance(this);

        // 初始化车辆录制控制器
        try {
            vehicleController = DIContainer.provideVehicleRecordingController(this);
            vehicleController.initialize();
            LogUtil.i(TAG, "VehicleRecordingController initialized successfully");
        } catch (Exception e) {
            LogUtil.e(TAG, "Failed to initialize VehicleRecordingController", e);
            // 车辆控制器初始化失败不影响基本录制功能
        }

        createNotificationChannel();
        startForeground(NOTIFICATION_ID, createNotification());

        LogUtil.d(TAG, "CameraService created with Repository pattern and Vehicle control");
    }

    @Override
    public IBinder onBind(Intent intent) {
        return binder;
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (intent != null) {
            String action = intent.getAction();
            if ("START_RECORDING".equals(action)) {
                startAllCamerasRecording();
            } else if ("STOP_RECORDING".equals(action)) {
                stopAllCamerasRecording();
            }
        }
        return START_STICKY;
    }

    @Override
    public void onTaskRemoved(Intent rootIntent) {
        updateNotification("应用已关闭，录制继续在后台运行");
        super.onTaskRemoved(rootIntent);
    }

    @Override
    public void onLowMemory() {
        updateNotification("系统内存不足，录制可能不稳定");
        super.onLowMemory();
    }

    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                CHANNEL_ID,
                "相机服务",
                NotificationManager.IMPORTANCE_LOW
            );
            channel.setDescription("用于保持相机服务在后台运行");
            NotificationManager manager = getSystemService(NotificationManager.class);
            if (manager != null) {
                manager.createNotificationChannel(channel);
            }
        }
    }

    private Notification createNotification() {
        Intent notificationIntent = new Intent(this, MainActivity.class);
        PendingIntent pendingIntent = PendingIntent.getActivity(
            this, 0, notificationIntent,
            PendingIntent.FLAG_IMMUTABLE
        );

        return new NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("哨兵服务运行中")
            .setContentText("哨兵服务运行中")
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentIntent(pendingIntent)
            .build();
    }

    /**
     * 使用Repository模式的录制管理方法
     */
    public void startAllCamerasRecording() {
        if (!isRunning) {
            isRunning = true;
            LogUtil.i(TAG, "Starting all cameras recording via Repository");

            // 通过Repository启动所有相机录制
            String basePath = CameraConstants.DEFAULT_RECORD_PATH;
            cameraRepository.startAllRecording(basePath);

            updateNotification("正在录制所有摄像头");
        }
    }

    public void stopAllCamerasRecording() {
        if (isRunning) {
            isRunning = false;
            LogUtil.i(TAG, "Stopping all cameras recording via Repository");

            // 通过Repository停止所有相机录制
            cameraRepository.stopAllRecording();

            updateNotification("录制已停止");
        }
    }

    /**
     * 单个相机录制控制
     */
    public void startCameraRecording(String cameraId) {
        LogUtil.i(TAG, "Starting recording for camera " + cameraId);
        String outputPath = CameraConstants.DEFAULT_RECORD_PATH + "/" +
                          CameraConstants.getCameraName(cameraId);
        cameraRepository.startRecording(cameraId, outputPath);
    }

    public void stopCameraRecording(String cameraId) {
        LogUtil.i(TAG, "Stopping recording for camera " + cameraId);
        cameraRepository.stopRecording(cameraId);
    }

    /**
     * 获取录制状态
     */
    public boolean isRecording() {
        return isRunning && cameraRepository.isAllCamerasRecording();
    }

    /**
     * 获取车辆录制控制器
     */
    public VehicleRecordingController getVehicleController() {
        return vehicleController;
    }

    /**
     * 手动启动录制（带车辆条件检查）
     */
    public void startRecordingWithVehicleCheck(VehicleRecordingController.ConditionCallback callback) {
        if (vehicleController != null) {
            vehicleController.startManualRecording(callback);
        } else {
            // 降级到普通录制
            LogUtil.w(TAG, "VehicleController not available, falling back to normal recording");
            startAllCamerasRecording();
            if (callback != null) {
                callback.onConditionMet();
            }
        }
    }

    /**
     * 手动停止录制（仅在哨兵模式关闭时可用）
     */
    public void stopRecordingManual() {
        if (vehicleController != null) {
            vehicleController.stopManualRecording();
        } else {
            // 降级到普通停止
            stopAllCamerasRecording();
        }
    }

    /**
     * 设置哨兵自动模式
     */
    public void setSentryAutoMode(boolean enabled) {
        if (vehicleController != null) {
            vehicleController.setSentryAutoMode(enabled);
        } else {
            LogUtil.w(TAG, "VehicleController not available, sentry mode not supported");
        }
    }

    /**
     * 兼容性方法 - 保持向后兼容
     * @deprecated 使用 startAllCamerasRecording() 替代
     */
    @Deprecated
    public void startRecording() {
        startAllCamerasRecording();
    }

    /**
     * @deprecated 使用 stopAllCamerasRecording() 替代
     */
    @Deprecated
    public void stopRecording() {
        stopAllCamerasRecording();
    }

    private void updateNotification(String contentText) {
        NotificationManager manager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
        if (manager != null) {
            Notification notification = new NotificationCompat.Builder(this, CHANNEL_ID)
                .setContentTitle("相机服务运行中")
                .setContentText(contentText)
                .setSmallIcon(R.drawable.ic_launcher_foreground)
                .build();
            manager.notify(NOTIFICATION_ID, notification);
        }
    }

    @Override
    public void onDestroy() {
        // 停止所有录制
        if (isRunning) {
            stopAllCamerasRecording();
        }

        // 清理车辆录制控制器
        if (vehicleController != null) {
            vehicleController.cleanup();
        }

        // 释放Repository资源
        if (cameraRepository != null) {
            cameraRepository.release();
        }
        if (recordingRepository != null) {
            recordingRepository.release();
        }

        super.onDestroy();
        LogUtil.d(TAG, "CameraService destroyed");
    }
} 