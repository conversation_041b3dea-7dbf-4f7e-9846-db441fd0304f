<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 应用主色调 - 使用位置：themes.xml主题定义、button_outline.xml按钮边框、导航栏颜色 -->
    <color name="colorPrimary">#3F51B5</color>
    <!-- 深色主色调 - 使用位置：themes.xml主题定义、navigation_bar_color导航栏 -->
    <color name="colorPrimaryDark">#303F9F</color>
    <!-- 强调色 - 使用位置：themes.xml主题定义 -->
    <color name="colorAccent">#FF4081</color>
    
    <!-- 基础白色 - 使用位置：themes.xml主题定义、activity_main.xml哨兵功能文字 -->
    <color name="white">#FFFFFF</color>


    <!-- 浅色背景 - 使用位置：基础颜色定义 -->
    <color name="background_light">#DEE2E5</color>

    <!-- 日夜模式适配颜色 - 日间模式 -->
    <!-- 主要文本颜色 - 使用位置：ThemeManager.java、ThemeApplier.java、通过text_adaptive在activity_main.xml和item_video_record.xml -->
    <color name="text_primary_adaptive">#000000</color>
    <!-- 次要文本颜色 - 使用位置：ThemeManager.java、ThemeApplier.java、item_video_record.xml录制时间 -->
    <color name="text_secondary_adaptive">#666666</color>
    <!-- 主要背景颜色 - 使用位置：ThemeManager.java、ThemeApplier.java、MainActivity.java各页面背景 -->
    <color name="background_primary_adaptive">#DEE2E5</color>
    <!-- 次要背景颜色 - 使用位置：ThemeManager.java -->
    <color name="background_secondary_adaptive">#DEE2E5</color>
    <!-- 容器背景颜色 - 使用位置：ThemeManager.java、ThemeApplier.java、MainActivity.java相机容器背景 -->
    <color name="container_background_adaptive">#F5F5F5</color>
    <!-- 选中按钮文字颜色 - 使用位置：ThemeManager.java、ThemeApplier.java、MainActivity.java页面按钮 -->
    <color name="button_text_selected_adaptive">#000000</color>
    <!-- 未选中按钮文字颜色 - 使用位置：ThemeManager.java、ThemeApplier.java、activity_main.xml筛选按钮 -->
    <color name="button_text_unselected_adaptive">#808080</color>

    <!-- 系统UI颜色 -->
    <!-- 状态栏颜色 - 使用位置：themes.xml、ThemeManager.java、MainActivity.setupStatusBar() -->
    <color name="status_bar_color">#DEE2E5</color>

    <!-- 按钮相关颜色 -->
    <!-- 选中按钮背景颜色 - 使用位置：button_background.xml -->
    <color name="button_background_selected">#44676767</color>
    <!-- 按钮边框颜色 - 使用位置：button_outline.xml -->
    <color name="button_outline_color">@color/colorPrimary</color>

    <!-- 时间选择器对话框专用颜色 - 日间模式 -->
    <!-- NumberPicker文字颜色 - 使用位置：TimePickerManager.java中NumberPicker的数字显示 -->
    <color name="number_picker_text_color">#000000</color>
    <!-- 分隔线颜色 - 使用位置：TimePickerManager.java中月/日/时选择器之间的分隔线 -->
    <color name="separator_line_color">#9C9C9C</color>
    <!-- 对话框标签文字颜色 - 使用位置：TimePickerManager.java中"月份"、"日期"、"小时"标签 -->
    <color name="dialog_label_text_color">#9C9C9C</color>
    <!-- 对话框背景颜色 - 使用位置：TimePickerManager.java中时间选择对话框背景 -->
    <color name="dialog_background_color">#CED0D1</color>

    <!-- 播放器控制栏专用颜色 - 日间模式 -->
    <!-- 播放器控制栏文字颜色 - 使用位置：MainActivity.java播放器控制栏按钮和文字，固定白色因为有半透明黑色背景 -->
    <color name="player_control_text_color">#FFFFFF</color>


    <!-- 兼容性保留颜色 - 为了保持向后兼容性 -->
    <!-- 适配文本颜色 - 使用位置：activity_main.xml筛选按钮、item_video_record.xml摄像头方向文字 -->
    <color name="text_adaptive">@color/text_primary_adaptive</color>

    <!-- 未选中按钮文字颜色 - 使用位置：activity_main.xml摄像头筛选按钮、时间筛选按钮 -->
    <color name="button_text_unselected">@color/button_text_unselected_adaptive</color>
</resources>