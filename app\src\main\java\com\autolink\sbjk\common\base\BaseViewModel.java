package com.autolink.sbjk.common.base;

import androidx.lifecycle.ViewModel;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.LiveData;
import android.util.Log;

import com.autolink.sbjk.common.util.LogUtil;
import com.autolink.sbjk.core.thread.ThreadManager;

import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * ViewModel基类
 * 提供通用的状态管理和错误处理机制
 */
public abstract class BaseViewModel extends ViewModel {
    
    private static final String TAG = "BaseViewModel";
    
    // 加载状态
    protected final MutableLiveData<Boolean> _isLoading = new MutableLiveData<>(false);
    public final LiveData<Boolean> isLoading = _isLoading;
    
    // 错误信息
    protected final MutableLiveData<String> _errorMessage = new MutableLiveData<>();
    public final LiveData<String> errorMessage = _errorMessage;
    
    // 成功消息
    protected final MutableLiveData<String> _successMessage = new MutableLiveData<>();
    public final LiveData<String> successMessage = _successMessage;
    
    // ViewModel是否已被清理
    private final AtomicBoolean isCleared = new AtomicBoolean(false);

    // 异步任务管理
    private final Set<Future<?>> activeTasks = ConcurrentHashMap.newKeySet();
    
    /**
     * 设置加载状态
     */
    protected void setLoading(boolean loading) {
        if (!isCleared.get()) {
            _isLoading.postValue(loading);
        }
    }
    
    /**
     * 设置错误信息
     */
    protected void setError(String message) {
        if (!isCleared.get()) {
            LogUtil.e(TAG, "Error: " + message);
            _errorMessage.postValue(message);
            setLoading(false);
        }
    }
    
    /**
     * 设置错误信息（带异常）
     */
    protected void setError(String message, Throwable throwable) {
        if (!isCleared.get()) {
            LogUtil.e(TAG, message, throwable);
            _errorMessage.postValue(message + ": " + throwable.getMessage());
            setLoading(false);
        }
    }
    
    /**
     * 设置成功消息
     */
    protected void setSuccess(String message) {
        if (!isCleared.get()) {
            LogUtil.i(TAG, "Success: " + message);
            _successMessage.postValue(message);
            setLoading(false);
        }
    }
    
    /**
     * 清除错误信息
     */
    public void clearError() {
        if (!isCleared.get()) {
            _errorMessage.postValue(null);
        }
    }
    
    /**
     * 清除成功消息
     */
    public void clearSuccess() {
        if (!isCleared.get()) {
            _successMessage.postValue(null);
        }
    }
    
    /**
     * 检查ViewModel是否已被清理
     */
    protected boolean isCleared() {
        return isCleared.get();
    }
    
    /**
     * 安全执行操作（检查是否已清理）
     */
    protected void safeExecute(Runnable action) {
        if (!isCleared.get()) {
            try {
                action.run();
            } catch (Exception e) {
                setError("操作执行失败", e);
            }
        }
    }
    
    /**
     * 异步执行操作（使用ThreadManager）
     */
    protected void executeAsync(Runnable action) {
        executeAsyncWithLifecycle(action);
    }

    /**
     * 异步执行操作并管理生命周期
     */
    protected void executeAsyncWithLifecycle(Runnable action) {
        if (!isCleared.get()) {
            Future<?> task = ThreadManager.getInstance().submitBackgroundTask(() -> {
                if (!isCleared.get()) {
                    try {
                        action.run();
                    } catch (Exception e) {
                        setError("异步操作失败", e);
                    }
                }
            });

            if (task != null) {
                activeTasks.add(task);
                // 任务完成后从集合中移除
                ThreadManager.getInstance().executeBackgroundTask(() -> {
                    try {
                        task.get(); // 等待任务完成
                    } catch (Exception e) {
                        // 任务被取消或执行失败，忽略
                    } finally {
                        activeTasks.remove(task);
                    }
                });
            }
        }
    }
    
    @Override
    protected void onCleared() {
        super.onCleared();
        isCleared.set(true);

        // 取消所有活跃的异步任务
        for (Future<?> task : activeTasks) {
            if (!task.isDone()) {
                task.cancel(true);
            }
        }
        activeTasks.clear();

        LogUtil.d(TAG, getClass().getSimpleName() + " cleared with " + activeTasks.size() + " tasks cancelled");

        // 子类可以重写此方法进行资源清理
        onViewModelCleared();
    }
    
    /**
     * 子类重写此方法进行特定的资源清理
     */
    protected void onViewModelCleared() {
        // 默认空实现
    }
    
    /**
     * 获取当前加载状态
     */
    protected boolean getCurrentLoadingState() {
        Boolean loading = _isLoading.getValue();
        return loading != null && loading;
    }
}
