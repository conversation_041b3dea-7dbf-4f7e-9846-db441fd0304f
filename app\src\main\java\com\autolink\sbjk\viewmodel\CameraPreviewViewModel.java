package com.autolink.sbjk.viewmodel;

import android.app.Application;
import android.view.Surface;

import androidx.annotation.NonNull;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.autolink.sbjk.common.base.BaseViewModel;
import com.autolink.sbjk.common.constant.CameraConstants;
import com.autolink.sbjk.common.util.LogUtil;
import com.autolink.sbjk.model.repository.CameraRepository;
import com.autolink.sbjk.model.repository.RecordingRepository;

/**
 * 相机预览ViewModel
 * 管理单个相机的预览和录制逻辑
 */
public class CameraPreviewViewModel extends BaseViewModel {
    
    private static final String TAG = "CameraPreviewViewModel";
    
    private final CameraRepository cameraRepository;
    private final RecordingRepository recordingRepository;
    
    // UI状态
    private final MutableLiveData<Boolean> _isRecording = new MutableLiveData<>(false);
    public final LiveData<Boolean> isRecording = _isRecording;
    
    private final MutableLiveData<String> _currentCameraId = new MutableLiveData<>(CameraConstants.CAMERA_FRONT);
    public final LiveData<String> currentCameraId = _currentCameraId;
    
    private final MutableLiveData<Boolean> _isCameraReady = new MutableLiveData<>(false);
    public final LiveData<Boolean> isCameraReady = _isCameraReady;
    
    private final MutableLiveData<String> _recordingDuration = new MutableLiveData<>("00:00:00");
    public final LiveData<String> recordingDuration = _recordingDuration;
    
    // 录制开始时间
    private long recordingStartTime = 0;
    private final android.os.Handler uiHandler = new android.os.Handler(android.os.Looper.getMainLooper());
    private Runnable durationUpdateRunnable;
    
    // 主构造函数 - 支持依赖注入
    public CameraPreviewViewModel(@NonNull CameraRepository cameraRepository,
                                 @NonNull RecordingRepository recordingRepository) {
        super();
        
        this.cameraRepository = cameraRepository;
        this.recordingRepository = recordingRepository;
        
        initializeDurationUpdater();
        
        LogUtil.d(TAG, "CameraPreviewViewModel initialized with injected dependencies");
    }
    
    // 兼容性构造函数 - 用于现有代码
    public CameraPreviewViewModel(@NonNull Application application) {
        this(CameraRepository.getInstance(application),
             RecordingRepository.getInstance(application));
        
        LogUtil.d(TAG, "CameraPreviewViewModel initialized with application context");
    }
    
    /**
     * 初始化录制时长更新器
     */
    private void initializeDurationUpdater() {
        durationUpdateRunnable = new Runnable() {
            @Override
            public void run() {
                if (!isCleared()) {
                    updateRecordingDuration();
                    uiHandler.postDelayed(this, 1000); // 每秒更新一次
                }
            }
        };
    }
    
    /**
     * 设置当前相机ID
     */
    public void setCurrentCameraId(String cameraId) {
        if (CameraConstants.isValidCameraId(cameraId)) {
            _currentCameraId.setValue(cameraId);
            LogUtil.d(TAG, "Current camera ID set to: " + cameraId);
        } else {
            LogUtil.w(TAG, "Invalid camera ID: " + cameraId);
            setError("无效的相机ID");
        }
    }
    
    /**
     * 预览Surface管理
     */
    public void onPreviewSurfaceAvailable(Surface surface) {
        safeExecute(() -> {
            String cameraId = _currentCameraId.getValue();
            if (cameraId != null) {
                LogUtil.d(TAG, "Preview surface available for camera " + cameraId);
                cameraRepository.setPreviewSurface(cameraId, surface);
                _isCameraReady.postValue(true);
            }
        });
    }
    
    public void onPreviewSurfaceChanged(Surface surface) {
        safeExecute(() -> {
            String cameraId = _currentCameraId.getValue();
            if (cameraId != null) {
                LogUtil.d(TAG, "Preview surface changed for camera " + cameraId);
                cameraRepository.updatePreviewSurface(cameraId, surface);
            }
        });
    }
    
    public void onPreviewSurfaceDestroyed() {
        safeExecute(() -> {
            String cameraId = _currentCameraId.getValue();
            if (cameraId != null) {
                LogUtil.d(TAG, "Preview surface destroyed for camera " + cameraId);
                cameraRepository.clearPreviewSurface(cameraId);
                _isCameraReady.postValue(false);
            }
        });
    }
    
    /**
     * 开始录制
     */
    public void startRecording() {
        if (getCurrentLoadingState()) {
            LogUtil.w(TAG, "Already in loading state, ignoring start request");
            return;
        }
        
        String cameraId = _currentCameraId.getValue();
        if (cameraId == null) {
            setError("相机未准备就绪");
            return;
        }
        
        setLoading(true);
        
        safeExecute(() -> {
            try {
                String outputPath = CameraConstants.DEFAULT_RECORD_PATH + "/" + 
                                  CameraConstants.getCameraName(cameraId) + "_preview";
                
                cameraRepository.startRecording(cameraId, outputPath);
                
                // 开始录制时长计时
                recordingStartTime = System.currentTimeMillis();
                uiHandler.post(durationUpdateRunnable);
                
                _isRecording.postValue(true);
                setSuccess("开始录制");
                
                LogUtil.i(TAG, "Recording started for camera " + cameraId);
                
            } catch (Exception e) {
                LogUtil.e(TAG, "Failed to start recording", e);
                setError("启动录制失败: " + e.getMessage());
            } finally {
                setLoading(false);
            }
        });
    }
    
    /**
     * 停止录制
     */
    public void stopRecording() {
        if (getCurrentLoadingState()) {
            LogUtil.w(TAG, "Already in loading state, ignoring stop request");
            return;
        }
        
        String cameraId = _currentCameraId.getValue();
        if (cameraId == null) {
            return;
        }
        
        setLoading(true);
        
        safeExecute(() -> {
            try {
                cameraRepository.stopRecording(cameraId);
                
                // 停止录制时长计时
                uiHandler.removeCallbacks(durationUpdateRunnable);
                recordingStartTime = 0;
                _recordingDuration.postValue("00:00:00");
                
                _isRecording.postValue(false);
                setSuccess("录制已停止");
                
                LogUtil.i(TAG, "Recording stopped for camera " + cameraId);
                
            } catch (Exception e) {
                LogUtil.e(TAG, "Failed to stop recording", e);
                setError("停止录制失败: " + e.getMessage());
            } finally {
                setLoading(false);
            }
        });
    }
    
    /**
     * 切换录制状态
     */
    public void toggleRecording() {
        Boolean isCurrentlyRecording = _isRecording.getValue();
        if (isCurrentlyRecording != null && isCurrentlyRecording) {
            stopRecording();
        } else {
            startRecording();
        }
    }
    
    /**
     * 更新录制时长
     */
    private void updateRecordingDuration() {
        if (recordingStartTime > 0) {
            long duration = System.currentTimeMillis() - recordingStartTime;
            String formattedDuration = formatDuration(duration);
            _recordingDuration.postValue(formattedDuration);
        }
    }
    
    /**
     * 格式化录制时长
     */
    private String formatDuration(long durationMs) {
        long seconds = durationMs / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        
        seconds = seconds % 60;
        minutes = minutes % 60;
        
        return String.format("%02d:%02d:%02d", hours, minutes, seconds);
    }
    
    /**
     * 获取当前录制状态
     */
    public boolean isCurrentlyRecording() {
        Boolean recording = _isRecording.getValue();
        return recording != null && recording;
    }
    
    /**
     * 获取当前相机ID
     */
    public String getCurrentCameraId() {
        return _currentCameraId.getValue();
    }
    
    /**
     * 检查相机是否准备就绪
     */
    public boolean isCameraCurrentlyReady() {
        Boolean ready = _isCameraReady.getValue();
        return ready != null && ready;
    }

    @Override
    protected void onCleared() {
        super.onCleared();

        // 清理资源
        if (uiHandler != null && durationUpdateRunnable != null) {
            uiHandler.removeCallbacks(durationUpdateRunnable);
        }

        // 如果正在录制，停止录制
        if (isCurrentlyRecording()) {
            stopRecording();
        }

        LogUtil.d(TAG, "CameraPreviewViewModel cleared");
    }
}
