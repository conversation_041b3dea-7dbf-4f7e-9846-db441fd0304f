# 车辆服务系统应用对接指南 - registerCallback接口

## 文档概述

本文档详细说明了系统应用如何对接车辆服务(VehicleService)的registerCallback接口，以实现对车辆数据的监听和控制。通过本指南，系统应用开发者可以完成与车辆底层数据的交互，实现车况监控、车辆控制等功能。

## 1. 简介

### 1.1 接口用途

registerCallback接口是车辆服务(VehicleService)为系统应用提供的核心接口，允许系统应用：
- 监听车辆状态变化（如钥匙状态、发动机状态、车速等）
- 接收车辆各系统实时数据（如空调温度、座椅状态等）
- 控制车辆各项功能（如调节空调、控制车窗等）

### 1.2 接口特权

与需要包名验证的registerRemoteCallback接口不同，registerCallback接口专为系统应用设计，具有以下特点：
- 无需包名验证
- 可访问所有车辆属性
- 具有完整的读写权限

### 1.3 前提条件

应用必须满足以下条件才能使用registerCallback接口：
- 应用必须具有系统权限（系统签名或安装在system分区）
- 应用需要声明相关权限
- 应用需要与VehicleService服务建立连接

## 2. 准备工作

### 2.1 添加权限声明

在应用的`AndroidManifest.xml`中添加必要的权限：

```xml
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.example.vehicleapp">

    <!-- 系统级权限 -->
    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS_FULL" />
    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS" />
    
    <!-- 可选：如需在后台持续监听车辆数据 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    
    <!-- 应用组件声明 -->
    <application
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme">
        <!-- 活动和服务组件 -->
    </application>
</manifest>
```

### 2.2 添加AIDL文件

创建`IVehicleControl.aidl`和`IVehicleControlCallback.aidl`文件，或者直接使用车辆服务提供的JAR包。

**IVehicleControlCallback.aidl**:
```java
package com.autolink.app.vehicleservice;

interface IVehicleControlCallback {
    void onReceiveInt(int propKey, int value, boolean selfGet);
    void onReceiveFloat(int propKey, float value, boolean selfGet);
    void onOnlySyncIntData(int propKey, int value);
    void onOnlySyncFloatData(int propKey, float value);
}
```

**IVehicleControl.aidl**:
```java
package com.autolink.app.vehicleservice;

import com.autolink.app.vehicleservice.IVehicleControlCallback;
import com.autolink.app.vehicleservice.IVehicleFrequentCallback;
import com.autolink.app.vehicleservice.AppointmentTimeBean;
import com.autolink.app.vehicleservice.CarModelColorBean;
import com.autolink.app.vehicleservice.ChargeInfoBean;
import com.autolink.app.vehicleservice.CustomCarSeatBean;
import com.autolink.app.vehicleservice.CustomWindowBean;
import com.autolink.app.vehicleservice.VehicleLicenseBean;

interface IVehicleControl {
    void registerCallback(IVehicleControlCallback callback, in int[] propertyKeys);
    void unregisterCallback(IVehicleControlCallback callback);
    
    void setInt(int propKey, int value);
    void setFloat(int propKey, float value);
    int getInt(int propKey);
    float getFloat(int propKey);
    
    ChargeInfoBean getChargeInfo(int propKey);
    CarModelColorBean geCarModelColor(int propKey);
    
    void setCustomWindow(int propKey, in CustomWindowBean value);
    void setCustomCarSeat(int propKey, in CustomCarSeatBean value);
    void setAppointmentTime(int propKey, in AppointmentTimeBean value);
    void setVehicleLicense(int propKey, in VehicleLicenseBean value);
    void setCarModelColor(int propKey, in CarModelColorBean value);
    
    // 更多接口方法...
}
```

## 3. 接口实现流程

### 3.1 创建VehicleManager类

首先创建一个VehicleManager类来管理与车辆服务的连接和通信：

```java
package com.example.vehicleapp;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.IBinder;
import android.os.RemoteException;
import android.util.Log;

import com.autolink.app.vehiclemanager.data.VehiclePropertyKey;
import com.autolink.app.vehicleservice.IVehicleControl;
import com.autolink.app.vehicleservice.IVehicleControlCallback;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

public class VehicleManager {
    private static final String TAG = "VehicleManager";
    
    private static VehicleManager sInstance;
    private Context mContext;
    private IVehicleControl mVehicleControl;
    private boolean mIsBound = false;
    private final List<VehicleDataListener> mListeners = new CopyOnWriteArrayList<>();
    
    // 单例模式
    public static synchronized VehicleManager getInstance(Context context) {
        if (sInstance == null) {
            sInstance = new VehicleManager(context.getApplicationContext());
        }
        return sInstance;
    }
    
    private VehicleManager(Context context) {
        mContext = context;
        bindVehicleService();
    }
    
    // 车辆数据监听器接口
    public interface VehicleDataListener {
        void onVehiclePropertyChanged(VehiclePropertyKey key, Object value, boolean selfGet);
    }
    
    // 添加监听器
    public void addListener(VehicleDataListener listener) {
        if (listener != null && !mListeners.contains(listener)) {
            mListeners.add(listener);
        }
    }
    
    // 移除监听器
    public void removeListener(VehicleDataListener listener) {
        if (listener != null) {
            mListeners.remove(listener);
        }
    }
    
    // 实现后续方法...
}
```

### 3.2 绑定车辆服务

在VehicleManager类中添加绑定服务的方法：

```java
// 绑定车辆服务
private void bindVehicleService() {
    if (mIsBound) {
        return;
    }
    
    Intent intent = new Intent();
    intent.setComponent(new ComponentName(
            "com.autolink.app.vehicleservice",
            "com.autolink.app.vehicleservice.VehicleService"));
    
    mIsBound = mContext.bindService(intent, mServiceConnection, Context.BIND_AUTO_CREATE);
    Log.d(TAG, "绑定车辆服务: " + (mIsBound ? "成功" : "失败"));
}

// 解绑车辆服务
public void unbindVehicleService() {
    if (mIsBound) {
        try {
            if (mVehicleControl != null && mCallback != null) {
                mVehicleControl.unregisterCallback(mCallback);
            }
        } catch (RemoteException e) {
            Log.e(TAG, "解注册回调失败", e);
        }
        
        mContext.unbindService(mServiceConnection);
        mVehicleControl = null;
        mIsBound = false;
        Log.d(TAG, "解绑车辆服务");
    }
}

// 服务连接回调
private final ServiceConnection mServiceConnection = new ServiceConnection() {
    @Override
    public void onServiceConnected(ComponentName name, IBinder service) {
        Log.d(TAG, "车辆服务已连接");
        mVehicleControl = IVehicleControl.Stub.asInterface(service);
        registerVehicleCallbacks();
    }

    @Override
    public void onServiceDisconnected(ComponentName name) {
        Log.d(TAG, "车辆服务已断开");
        mVehicleControl = null;
        mIsBound = false;
        
        // 可选：尝试重新绑定
        new Thread(() -> {
            try {
                Thread.sleep(3000);
                bindVehicleService();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }).start();
    }
};
```

### 3.3 实现回调接口

实现IVehicleControlCallback接口，处理来自车辆服务的回调：

```java
// 车辆服务回调实现
private final IVehicleControlCallback mCallback = new IVehicleControlCallback.Stub() {
    @Override
    public void onReceiveInt(int propKey, int value, boolean selfGet) throws RemoteException {
        VehiclePropertyKey key = VehiclePropertyKey.INSTANCE.valueOf(propKey);
        if (key != null) {
            Log.d(TAG, "接收整型属性: " + key.getDesc() + " = " + value + ", selfGet=" + selfGet);
            notifyListeners(key, value, selfGet);
        }
    }

    @Override
    public void onReceiveFloat(int propKey, float value, boolean selfGet) throws RemoteException {
        VehiclePropertyKey key = VehiclePropertyKey.INSTANCE.valueOf(propKey);
        if (key != null) {
            Log.d(TAG, "接收浮点型属性: " + key.getDesc() + " = " + value + ", selfGet=" + selfGet);
            notifyListeners(key, value, selfGet);
        }
    }

    @Override
    public void onOnlySyncIntData(int propKey, int value) throws RemoteException {
        VehiclePropertyKey key = VehiclePropertyKey.INSTANCE.valueOf(propKey);
        if (key != null) {
            Log.d(TAG, "接收同步整型属性: " + key.getDesc() + " = " + value);
            notifyListeners(key, value, false);
        }
    }

    @Override
    public void onOnlySyncFloatData(int propKey, float value) throws RemoteException {
        VehiclePropertyKey key = VehiclePropertyKey.INSTANCE.valueOf(propKey);
        if (key != null) {
            Log.d(TAG, "接收同步浮点型属性: " + key.getDesc() + " = " + value);
            notifyListeners(key, value, false);
        }
    }
};

// 通知所有监听器
private void notifyListeners(VehiclePropertyKey key, Object value, boolean selfGet) {
    for (VehicleDataListener listener : mListeners) {
        try {
            listener.onVehiclePropertyChanged(key, value, selfGet);
        } catch (Exception e) {
            Log.e(TAG, "通知监听器失败", e);
        }
    }
}
```

### 3.4 注册车辆属性回调

实现注册回调的方法，指定需要监听的车辆属性：

```java
// 注册车辆回调
private void registerVehicleCallbacks() {
    if (mVehicleControl == null) {
        Log.e(TAG, "车辆服务未连接，无法注册回调");
        return;
    }
    
    try {
        // 定义需要监听的属性
        int[] propertyKeys = new int[] {
            // 车辆状态
            VehiclePropertyKey.KeyVehiclePropertyKeySts.getValue(),      // 钥匙状态
            VehiclePropertyKey.KeyVehiclePropertyEngineSts.getValue(),   // 发动机状态
            VehiclePropertyKey.KeyVehiclePropertySpeed.getValue(),       // 车速
            
            // 空调系统
            VehiclePropertyKey.KeyHvacPower.getValue(),                  // 空调开关
            VehiclePropertyKey.KeyHvacTemperatureFL.getValue(),          // 主驾温度
            VehiclePropertyKey.KeyHvacTemperatureFR.getValue(),          // 副驾温度
            VehiclePropertyKey.KeyHvacFanSpeed.getValue(),               // 空调风速
            
            // 座椅控制
            VehiclePropertyKey.KeyHvacSeatHeatingFL.getValue(),          // 主驾座椅加热
            VehiclePropertyKey.KeyHvacSeatHeatingFR.getValue(),          // 副驾座椅加热
            
            // 车窗控制
            VehiclePropertyKey.KeyVehiclePropertyFLWindow.getValue(),    // 左前车窗
            VehiclePropertyKey.KeyVehiclePropertyFRWindow.getValue(),    // 右前车窗
            
            // 更多属性...
        };
        
        // 注册回调
        mVehicleControl.registerCallback(mCallback, propertyKeys);
        Log.d(TAG, "已注册车辆属性回调");
        
    } catch (RemoteException e) {
        Log.e(TAG, "注册车辆属性回调失败", e);
    }
}
```

### 3.5 读取车辆属性

实现读取车辆属性的方法：

```java
// 获取整型属性
public int getIntProperty(VehiclePropertyKey key) {
    if (mVehicleControl == null || key == null) {
        Log.e(TAG, "车辆服务未连接或属性键为空");
        return -1;
    }
    
    try {
        int value = mVehicleControl.getInt(key.getValue());
        Log.d(TAG, "获取整型属性: " + key.getDesc() + " = " + value);
        return value;
    } catch (RemoteException e) {
        Log.e(TAG, "获取整型属性失败: " + key.getDesc(), e);
        return -1;
    }
}

// 获取浮点型属性
public float getFloatProperty(VehiclePropertyKey key) {
    if (mVehicleControl == null || key == null) {
        Log.e(TAG, "车辆服务未连接或属性键为空");
        return -1.0f;
    }
    
    try {
        float value = mVehicleControl.getFloat(key.getValue());
        Log.d(TAG, "获取浮点型属性: " + key.getDesc() + " = " + value);
        return value;
    } catch (RemoteException e) {
        Log.e(TAG, "获取浮点型属性失败: " + key.getDesc(), e);
        return -1.0f;
    }
}

// 获取充电信息
public ChargeInfoBean getChargeInfo() {
    if (mVehicleControl == null) {
        Log.e(TAG, "车辆服务未连接");
        return null;
    }
    
    try {
        return mVehicleControl.getChargeInfo(VehiclePropertyKey.KeyPHEVCurrentVoltagePower.getValue());
    } catch (RemoteException e) {
        Log.e(TAG, "获取充电信息失败", e);
        return null;
    }
}

// 获取车模颜色
public CarModelColorBean getCarModelColor() {
    if (mVehicleControl == null) {
        Log.e(TAG, "车辆服务未连接");
        return null;
    }
    
    try {
        return mVehicleControl.geCarModelColor(VehiclePropertyKey.KeyVehiclePropertyCarModelColor.getValue());
    } catch (RemoteException e) {
        Log.e(TAG, "获取车模颜色失败", e);
        return null;
    }
}
```

### 3.6 写入车辆属性

实现设置车辆属性的方法：

```java
// 设置整型属性
public boolean setIntProperty(VehiclePropertyKey key, int value) {
    if (mVehicleControl == null || key == null) {
        Log.e(TAG, "车辆服务未连接或属性键为空");
        return false;
    }
    
    try {
        mVehicleControl.setInt(key.getValue(), value);
        Log.d(TAG, "设置整型属性: " + key.getDesc() + " = " + value);
        return true;
    } catch (RemoteException e) {
        Log.e(TAG, "设置整型属性失败: " + key.getDesc(), e);
        return false;
    }
}

// 设置浮点型属性
public boolean setFloatProperty(VehiclePropertyKey key, float value) {
    if (mVehicleControl == null || key == null) {
        Log.e(TAG, "车辆服务未连接或属性键为空");
        return false;
    }
    
    try {
        mVehicleControl.setFloat(key.getValue(), value);
        Log.d(TAG, "设置浮点型属性: " + key.getDesc() + " = " + value);
        return true;
    } catch (RemoteException e) {
        Log.e(TAG, "设置浮点型属性失败: " + key.getDesc(), e);
        return false;
    }
}

// 设置自定义车窗
public boolean setCustomWindow(CustomWindowBean value) {
    if (mVehicleControl == null) {
        Log.e(TAG, "车辆服务未连接");
        return false;
    }
    
    try {
        mVehicleControl.setCustomWindow(VehiclePropertyKey.KeyVehiclePropertyCustomCarWindow.getValue(), value);
        Log.d(TAG, "设置自定义车窗: " + value);
        return true;
    } catch (RemoteException e) {
        Log.e(TAG, "设置自定义车窗失败", e);
        return false;
    }
}

// 设置自定义座椅
public boolean setCustomCarSeat(CustomCarSeatBean value) {
    if (mVehicleControl == null) {
        Log.e(TAG, "车辆服务未连接");
        return false;
    }
    
    try {
        mVehicleControl.setCustomCarSeat(VehiclePropertyKey.KeyVehiclePropertyCustomCarSeat.getValue(), value);
        Log.d(TAG, "设置自定义座椅: " + value);
        return true;
    } catch (RemoteException e) {
        Log.e(TAG, "设置自定义座椅失败", e);
        return false;
    }
}

// 设置车牌号
public boolean setVehicleLicense(VehicleLicenseBean value) {
    if (mVehicleControl == null) {
        Log.e(TAG, "车辆服务未连接");
        return false;
    }
    
    try {
        mVehicleControl.setVehicleLicense(VehiclePropertyKey.KeyVehiclePropertyLicense.getValue(), value);
        Log.d(TAG, "设置车牌号: " + value);
        return true;
    } catch (RemoteException e) {
        Log.e(TAG, "设置车牌号失败", e);
        return false;
    }
}
```

## 4. 使用示例

### 4.1 Activity中使用VehicleManager

下面展示如何在Activity中使用VehicleManager：

```java
public class MainActivity extends AppCompatActivity implements VehicleManager.VehicleDataListener {
    private static final String TAG = "MainActivity";
    private VehicleManager mVehicleManager;
    private TextView mKeyStateTextView;
    private TextView mEngineStateTextView;
    private TextView mSpeedTextView;
    private TextView mTempFLTextView;
    private Switch mAcSwitch;
    private SeekBar mTempSeekBar;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        
        // 初始化视图
        mKeyStateTextView = findViewById(R.id.tv_key_state);
        mEngineStateTextView = findViewById(R.id.tv_engine_state);
        mSpeedTextView = findViewById(R.id.tv_speed);
        mTempFLTextView = findViewById(R.id.tv_temp_fl);
        mAcSwitch = findViewById(R.id.switch_ac);
        mTempSeekBar = findViewById(R.id.seekbar_temp);
        
        // 获取VehicleManager实例
        mVehicleManager = VehicleManager.getInstance(this);
        
        // 设置控件监听器
        mAcSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
            mVehicleManager.setIntProperty(VehiclePropertyKey.KeyHvacPower, isChecked ? 1 : 0);
        });
        
        mTempSeekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (fromUser) {
                    // 温度范围例如18.0-30.0摄氏度
                    float temp = 18.0f + (progress / 100.0f) * 12.0f;
                    mVehicleManager.setFloatProperty(VehiclePropertyKey.KeyHvacTemperatureFL, temp);
                }
            }
            
            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {}
            
            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {}
        });
        
        // 刷新当前状态
        refreshVehicleState();
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        // 注册车辆数据监听器
        mVehicleManager.addListener(this);
    }
    
    @Override
    protected void onPause() {
        super.onPause();
        // 注销车辆数据监听器
        mVehicleManager.removeListener(this);
    }
    
    // 刷新车辆状态
    private void refreshVehicleState() {
        // 获取钥匙状态
        int keyState = mVehicleManager.getIntProperty(VehiclePropertyKey.KeyVehiclePropertyKeySts);
        updateKeyStateUI(keyState);
        
        // 获取发动机状态
        int engineState = mVehicleManager.getIntProperty(VehiclePropertyKey.KeyVehiclePropertyEngineSts);
        updateEngineStateUI(engineState);
        
        // 获取空调开关状态
        int acState = mVehicleManager.getIntProperty(VehiclePropertyKey.KeyHvacPower);
        mAcSwitch.setChecked(acState == 1);
        
        // 获取温度
        float tempFL = mVehicleManager.getFloatProperty(VehiclePropertyKey.KeyHvacTemperatureFL);
        updateTempUI(tempFL);
    }
    
    // 更新钥匙状态UI
    private void updateKeyStateUI(int keyState) {
        String stateText;
        switch (keyState) {
            case 0:
                stateText = "钥匙Off";
                break;
            case 1:
                stateText = "钥匙ACC";
                break;
            case 2:
                stateText = "钥匙On";
                break;
            case 3:
                stateText = "钥匙Crank";
                break;
            default:
                stateText = "未知状态";
                break;
        }
        mKeyStateTextView.setText("钥匙状态: " + stateText);
    }
    
    // 更新发动机状态UI
    private void updateEngineStateUI(int engineState) {
        String stateText = engineState == 1 ? "发动机运行中" : "发动机停止";
        mEngineStateTextView.setText("发动机状态: " + stateText);
    }
    
    // 更新温度UI
    private void updateTempUI(float temp) {
        mTempFLTextView.setText(String.format("主驾温度: %.1f℃", temp));
        // 更新进度条
        int progress = (int)((temp - 18.0f) / 12.0f * 100);
        mTempSeekBar.setProgress(progress);
    }
    
    // 实现VehicleDataListener接口
    @Override
    public void onVehiclePropertyChanged(VehiclePropertyKey key, Object value, boolean selfGet) {
        // 在UI线程更新界面
        runOnUiThread(() -> {
            if (key == VehiclePropertyKey.KeyVehiclePropertyKeySts) {
                updateKeyStateUI((Integer) value);
            } else if (key == VehiclePropertyKey.KeyVehiclePropertyEngineSts) {
                updateEngineStateUI((Integer) value);
            } else if (key == VehiclePropertyKey.KeyVehiclePropertySpeed) {
                float speed = (Float) value;
                mSpeedTextView.setText(String.format("车速: %.1f km/h", speed));
            } else if (key == VehiclePropertyKey.KeyHvacPower) {
                boolean isOn = ((Integer) value) == 1;
                mAcSwitch.setChecked(isOn);
            } else if (key == VehiclePropertyKey.KeyHvacTemperatureFL) {
                updateTempUI((Float) value);
            }
        });
    }
}
```

### 4.2 Service中使用VehicleManager

如果需要在后台持续监控车辆数据，可以创建一个Service：

```java
public class VehicleMonitorService extends Service implements VehicleManager.VehicleDataListener {
    private static final String TAG = "VehicleMonitorService";
    private static final String CHANNEL_ID = "vehicle_monitor_channel";
    private static final int NOTIFICATION_ID = 1001;
    
    private VehicleManager mVehicleManager;
    private boolean mIsEngineRunning = false;
    
    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "服务创建");
        
        // 创建通知渠道（Android 8.0及以上版本需要）
        createNotificationChannel();
        
        // 获取VehicleManager实例
        mVehicleManager = VehicleManager.getInstance(this);
        
        // 注册车辆数据监听器
        mVehicleManager.addListener(this);
        
        // 初始获取发动机状态
        int engineState = mVehicleManager.getIntProperty(VehiclePropertyKey.KeyVehiclePropertyEngineSts);
        mIsEngineRunning = engineState == 1;
        
        // 启动前台服务
        startForeground(NOTIFICATION_ID, createNotification());
    }
    
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.d(TAG, "服务启动");
        return START_STICKY;
    }
    
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }
    
    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "服务销毁");
        
        // 注销车辆数据监听器
        if (mVehicleManager != null) {
            mVehicleManager.removeListener(this);
        }
    }
    
    // 创建通知渠道
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                    CHANNEL_ID,
                    "车辆监控服务",
                    NotificationManager.IMPORTANCE_LOW);
            channel.setDescription("用于监控车辆状态的后台服务");
            
            NotificationManager notificationManager = getSystemService(NotificationManager.class);
            notificationManager.createNotificationChannel(channel);
        }
    }
    
    // 创建通知
    private Notification createNotification() {
        NotificationCompat.Builder builder = new NotificationCompat.Builder(this, CHANNEL_ID)
                .setSmallIcon(R.drawable.ic_car)
                .setContentTitle("车辆监控服务")
                .setContentText(mIsEngineRunning ? "发动机运行中" : "发动机已停止")
                .setPriority(NotificationCompat.PRIORITY_LOW);
        
        return builder.build();
    }
    
    // 更新通知
    private void updateNotification() {
        NotificationManager notificationManager = getSystemService(NotificationManager.class);
        notificationManager.notify(NOTIFICATION_ID, createNotification());
    }
    
    // 实现VehicleDataListener接口
    @Override
    public void onVehiclePropertyChanged(VehiclePropertyKey key, Object value, boolean selfGet) {
        if (key == VehiclePropertyKey.KeyVehiclePropertyEngineSts) {
            boolean wasEngineRunning = mIsEngineRunning;
            mIsEngineRunning = ((Integer) value) == 1;
            
            // 如果发动机状态变化，更新通知
            if (wasEngineRunning != mIsEngineRunning) {
                Log.d(TAG, "发动机状态变化: " + (mIsEngineRunning ? "运行中" : "已停止"));
                updateNotification();
                
                // 可以在这里添加其他逻辑，如发送广播、保存数据等
            }
        }
        
        // 处理其他感兴趣的属性...
    }
}
```

## 5. 常见问题解答

### 5.1 绑定服务失败

**问题**：绑定车辆服务（VehicleService）失败。

**解决方案**：
1. 确认应用是否有系统权限（系统签名或安装在system分区）
2. 检查服务组件名是否正确（包名和类名）
3. 确认车辆服务是否正在运行
4. 添加绑定服务的错误处理和重试机制

### 5.2 回调方法不触发

**问题**：注册了回调，但回调方法未被触发。

**解决方案**：
1. 确认已正确注册了需要监听的属性
2. 检查所注册的属性键值是否正确
3. 确认回调对象在服务连接期间保持有效（不被垃圾回收）
4. 增加日志检查registerCallback调用是否成功

### 5.3 属性值读写失败

**问题**：读取或设置车辆属性值失败。

**解决方案**：
1. 确认VehicleControl接口对象是否有效（服务是否已连接）
2. 检查属性键值是否正确
3. 确认属性值的数据类型是否匹配（整型/浮点型）
4. 添加异常处理和错误恢复机制

### 5.4 ANR（应用无响应）问题

**问题**：在主线程中进行车辆服务操作导致ANR。

**解决方案**：
1. 避免在主线程中执行远程调用（如绑定服务、读写属性等）
2. 使用后台线程或协程处理车辆服务操作
3. 实现异步回调机制，将结果发送到主线程更新UI

### 5.5 内存泄漏

**问题**：服务连接和回调注册导致内存泄漏。

**解决方案**：
1. 在组件（Activity、Fragment、Service）销毁时解注册回调
2. 在组件销毁时解绑服务
3. 使用弱引用持有上下文对象
4. 避免在静态变量中持有回调对象

## 6. 最佳实践

### 6.1 数据处理

1. **异步处理**：在后台线程中处理车辆数据，避免阻塞主线程
2. **数据缓存**：缓存常用的车辆属性值，减少远程调用
3. **批量更新**：收集多个属性变化后一次性更新UI，减少刷新次数
4. **数据验证**：检查和验证从车辆服务获取的数据，处理异常值

### 6.2 资源管理

1. **服务连接管理**：维护服务连接状态，实现自动重连机制
2. **回调管理**：集中管理回调注册和注销，避免重复注册
3. **生命周期管理**：与组件生命周期绑定，确保资源正确释放
4. **错误处理**：实现完善的错误处理和恢复机制

### 6.3 性能优化

1. **减少回调频率**：仅注册必要的属性回调，减少数据传输
2. **懒加载**：延迟加载不急需的车辆数据
3. **预加载**：预测用户可能需要的数据，提前加载
4. **条件刷新**：仅在数据真正变化时更新UI

### 6.4 功能扩展

1. **数据转换**：提供便捷的数据转换方法，如枚举值转字符串描述
2. **高级功能封装**：封装常用的功能组合，如一键开启/关闭空调系统
3. **状态监控**：实现车辆状态监控和异常报警功能
4. **数据记录**：记录和分析车辆数据，提供统计和报表功能

## 7. 结语

通过本文档，我们详细介绍了系统应用如何对接车辆服务的registerCallback接口，包括准备工作、接口实现流程、使用示例、常见问题解答和最佳实践。系统应用开发者可以根据本指南，快速实现与车辆数据的交互，开发出功能丰富、性能稳定的车载应用。

对接过程中，建议同时参考车辆服务的官方文档和示例代码，结合实际项目需求进行开发。如遇到特殊问题，可咨询车辆服务开发团队获取支持。
