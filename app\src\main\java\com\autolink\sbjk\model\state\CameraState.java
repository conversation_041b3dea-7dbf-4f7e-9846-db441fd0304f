package com.autolink.sbjk.model.state;

import com.autolink.sbjk.model.entity.CameraInfo;
import java.util.List;
import java.util.Map;

/**
 * 相机状态类
 * 管理所有相机的状态信息
 */
public class CameraState {
    
    private final Map<String, CameraInfo> cameraInfoMap;
    private final List<String> activeCameraIds;
    private boolean isAllCamerasRecording;
    private boolean isSystemReady;
    private long lastUpdateTime;
    private String globalError;
    
    public CameraState(Map<String, CameraInfo> cameraInfoMap, List<String> activeCameraIds) {
        this.cameraInfoMap = cameraInfoMap;
        this.activeCameraIds = activeCameraIds;
        this.isAllCamerasRecording = false;
        this.isSystemReady = false;
        this.lastUpdateTime = System.currentTimeMillis();
    }
    
    // Getters
    public Map<String, CameraInfo> getCameraInfoMap() { return cameraInfoMap; }
    public List<String> getActiveCameraIds() { return activeCameraIds; }
    public boolean isAllCamerasRecording() { return isAllCamerasRecording; }
    public boolean isSystemReady() { return isSystemReady; }
    public long getLastUpdateTime() { return lastUpdateTime; }
    public String getGlobalError() { return globalError; }
    
    // Setters
    public void setAllCamerasRecording(boolean allRecording) { 
        this.isAllCamerasRecording = allRecording;
        updateTimestamp();
    }
    
    public void setSystemReady(boolean ready) { 
        this.isSystemReady = ready;
        updateTimestamp();
    }
    
    public void setGlobalError(String error) { 
        this.globalError = error;
        updateTimestamp();
    }
    
    /**
     * 获取指定相机信息
     */
    public CameraInfo getCameraInfo(String cameraId) {
        return cameraInfoMap.get(cameraId);
    }
    
    /**
     * 更新相机信息
     */
    public void updateCameraInfo(String cameraId, CameraInfo cameraInfo) {
        cameraInfoMap.put(cameraId, cameraInfo);
        updateTimestamp();
    }
    
    /**
     * 获取正在录制的相机数量
     */
    public int getRecordingCameraCount() {
        int count = 0;
        for (CameraInfo info : cameraInfoMap.values()) {
            if (info.isRecording()) {
                count++;
            }
        }
        return count;
    }
    
    /**
     * 获取可用的相机数量
     */
    public int getAvailableCameraCount() {
        int count = 0;
        for (CameraInfo info : cameraInfoMap.values()) {
            if (info.isAvailable()) {
                count++;
            }
        }
        return count;
    }
    
    /**
     * 检查是否有相机出现错误
     */
    public boolean hasAnyError() {
        if (globalError != null && !globalError.isEmpty()) {
            return true;
        }
        
        for (CameraInfo info : cameraInfoMap.values()) {
            if (info.getLastError() != null && !info.getLastError().isEmpty()) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 获取所有错误信息
     */
    public String getAllErrors() {
        StringBuilder errors = new StringBuilder();
        
        if (globalError != null && !globalError.isEmpty()) {
            errors.append("系统错误: ").append(globalError).append("\n");
        }
        
        for (CameraInfo info : cameraInfoMap.values()) {
            String error = info.getLastError();
            if (error != null && !error.isEmpty()) {
                errors.append(info.getCameraName()).append("错误: ").append(error).append("\n");
            }
        }
        
        return errors.toString().trim();
    }
    
    /**
     * 清除所有错误
     */
    public void clearAllErrors() {
        globalError = null;
        for (CameraInfo info : cameraInfoMap.values()) {
            info.setLastError(null);
        }
        updateTimestamp();
    }
    
    /**
     * 检查系统是否处于健康状态
     */
    public boolean isHealthy() {
        return isSystemReady && 
               !hasAnyError() && 
               getAvailableCameraCount() == activeCameraIds.size();
    }
    
    /**
     * 更新时间戳
     */
    private void updateTimestamp() {
        this.lastUpdateTime = System.currentTimeMillis();
    }
    
    /**
     * 创建状态快照
     */
    public CameraState createSnapshot() {
        Map<String, CameraInfo> snapshotMap = new java.util.HashMap<>();
        for (Map.Entry<String, CameraInfo> entry : cameraInfoMap.entrySet()) {
            // 这里应该深拷贝CameraInfo，简化处理直接引用
            snapshotMap.put(entry.getKey(), entry.getValue());
        }
        
        CameraState snapshot = new CameraState(snapshotMap, new java.util.ArrayList<>(activeCameraIds));
        snapshot.isAllCamerasRecording = this.isAllCamerasRecording;
        snapshot.isSystemReady = this.isSystemReady;
        snapshot.lastUpdateTime = this.lastUpdateTime;
        snapshot.globalError = this.globalError;
        
        return snapshot;
    }
    
    @Override
    public String toString() {
        return "CameraState{" +
                "activeCameras=" + activeCameraIds.size() +
                ", recordingCameras=" + getRecordingCameraCount() +
                ", isAllRecording=" + isAllCamerasRecording +
                ", isSystemReady=" + isSystemReady +
                ", hasError=" + hasAnyError() +
                '}';
    }
}
