package com.autolink.sbjk.common.theme;

import android.content.Context;
import android.content.res.Configuration;
import android.graphics.Color;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import com.autolink.sbjk.R;
import com.autolink.sbjk.common.util.LogUtil;

import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 统一的主题管理器
 * 
 * 职责：
 * 1. 统一管理主题状态检测和缓存
 * 2. 提供主题变化通知机制
 * 3. 集中管理颜色资源获取
 * 4. 优化主题检测性能
 * 
 * 设计模式：
 * - 单例模式：确保全局唯一实例
 * - 观察者模式：主题变化通知机制
 * - 缓存模式：避免重复的主题检测
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-04
 */
public class ThemeManager {
    
    private static final String TAG = "ThemeManager";
    private static volatile ThemeManager instance;
    
    // 主题状态缓存
    private boolean isDarkMode = false;
    private boolean isInitialized = false;
    
    // 性能优化：节流机制
    private long lastUpdateTime = 0;
    private static final long UPDATE_THROTTLE_MS = 100; // 100ms内不重复检测
    
    // 观察者模式：LiveData支持
    private final MutableLiveData<Boolean> _themeState = new MutableLiveData<>();
    public final LiveData<Boolean> themeState = _themeState;
    
    // 主题变化监听器列表（线程安全）
    private final CopyOnWriteArrayList<ThemeChangeListener> listeners = new CopyOnWriteArrayList<>();
    
    // 颜色资源缓存
    private ThemeColors currentColors;
    
    /**
     * 主题变化监听器接口
     */
    public interface ThemeChangeListener {
        /**
         * 主题变化回调
         * @param isDarkMode 是否为深色模式
         * @param colors 当前主题颜色
         */
        void onThemeChanged(boolean isDarkMode, ThemeColors colors);
    }
    
    /**
     * 主题颜色资源类
     * 
     * 集中管理所有主题相关的颜色值，替换原有的硬编码颜色
     */
    public static class ThemeColors {
        // 文本颜色
        public final int textPrimary;           // 主要文本颜色 - 使用位置：MainActivity页面按钮、时间显示、哨兵功能文字
        public final int textSecondary;         // 次要文本颜色 - 使用位置：VideoListAdapter录制时间文字

        // 背景颜色
        public final int backgroundPrimary;     // 主要背景颜色 - 使用位置：MainActivity左右控制面板、录像回放页面、视频播放器页面
        public final int backgroundSecondary;   // 次要背景颜色 - 使用位置：预留扩展使用
        public final int containerBackground;   // 容器背景颜色 - 使用位置：MainActivity相机容器CardView背景

        // 按钮颜色
        public final int buttonTextSelected;    // 选中按钮文字颜色 - 使用位置：MainActivity页面切换按钮选中状态
        public final int buttonTextUnselected;  // 未选中按钮文字颜色 - 使用位置：MainActivity页面切换按钮未选中状态、FilterButtonManager筛选按钮

        // 系统UI颜色
        public final int statusBarColor;        // 状态栏颜色 - 使用位置：MainActivity.setupStatusBar()设置状态栏

        // 对话框专用颜色
        public final int numberPickerTextColor;  // NumberPicker文字颜色 - 使用位置：TimePickerManager对话框中的数字显示
        public final int separatorLineColor;     // 分隔线颜色 - 使用位置：TimePickerManager对话框中的分隔线
        public final int dialogLabelTextColor;   // 对话框标签文字颜色 - 使用位置：TimePickerManager对话框中的"月份"、"日期"、"小时"标签
        public final int dialogBackgroundColor;  // 对话框背景颜色 - 使用位置：TimePickerManager对话框的圆角背景
        
        /**
         * 构造主题颜色对象
         * 
         * 替换原有的硬编码颜色值：
         * - Color.parseColor("#DEE2E5") -> backgroundPrimary
         * - Color.parseColor("#CCCCCC") -> textSecondary (dark mode)
         * - Color.parseColor("#808080") -> buttonTextUnselected
         * - Color.parseColor("#1A1A1A") -> containerBackground (dark mode)
         * 
         * @param context Android上下文
         * @param isDarkMode 是否为深色模式
         */
        public ThemeColors(Context context, boolean isDarkMode) {
            // 【重构】使用颜色资源替代硬编码，利用Android自动日夜模式适配
            // 移除isDarkMode判断，直接使用adaptive颜色资源，让Android系统自动处理日夜模式切换

            // 主要颜色 - 使用adaptive颜色资源自动适配日夜模式
            textPrimary = ContextCompat.getColor(context, R.color.text_primary_adaptive);           // MainActivity时间显示、哨兵功能文字等主要文本颜色
            textSecondary = ContextCompat.getColor(context, R.color.text_secondary_adaptive);       // VideoListAdapter录制时间等次要文本颜色
            backgroundPrimary = ContextCompat.getColor(context, R.color.background_primary_adaptive); // MainActivity左右控制面板、录像回放页面等主要背景颜色
            backgroundSecondary = ContextCompat.getColor(context, R.color.background_secondary_adaptive); // 预留扩展使用的次要背景颜色
            containerBackground = ContextCompat.getColor(context, R.color.container_background_adaptive); // MainActivity相机容器CardView背景颜色
            buttonTextSelected = ContextCompat.getColor(context, R.color.button_text_selected_adaptive); // MainActivity页面切换按钮选中状态文字颜色
            buttonTextUnselected = ContextCompat.getColor(context, R.color.button_text_unselected_adaptive); // MainActivity页面切换按钮未选中状态、FilterButtonManager筛选按钮文字颜色
            statusBarColor = ContextCompat.getColor(context, R.color.status_bar_color);             // MainActivity状态栏背景颜色

            // 对话框专用颜色 - 使用adaptive颜色资源自动适配日夜模式
            numberPickerTextColor = ContextCompat.getColor(context, R.color.number_picker_text_color);   // TimePickerManager对话框中NumberPicker的数字文字颜色
            separatorLineColor = ContextCompat.getColor(context, R.color.separator_line_color);          // TimePickerManager对话框中月/日/时选择器之间的分隔线颜色
            dialogLabelTextColor = ContextCompat.getColor(context, R.color.dialog_label_text_color);     // TimePickerManager对话框中"月份"、"日期"、"小时"标签文字颜色
            dialogBackgroundColor = ContextCompat.getColor(context, R.color.dialog_background_color);    // TimePickerManager对话框的圆角背景颜色
        }
    }
    
    /**
     * 获取单例实例
     * 
     * 使用双重检查锁定模式确保线程安全
     */
    public static ThemeManager getInstance() {
        if (instance == null) {
            synchronized (ThemeManager.class) {
                if (instance == null) {
                    instance = new ThemeManager();
                }
            }
        }
        return instance;
    }
    
    /**
     * 私有构造函数，防止外部实例化
     */
    private ThemeManager() {
        // 私有构造函数
    }
    
    /**
     * 初始化主题管理器
     * 
     * 应在Application.onCreate()或MainActivity.onCreate()中调用
     * 
     * @param context Android上下文
     */
    public void initialize(Context context) {
        if (isInitialized) {
            LogUtil.d(TAG, "ThemeManager already initialized");
            return;
        }
        
        updateThemeState(context);
        isInitialized = true;
        LogUtil.d(TAG, "ThemeManager initialized with theme: " + (isDarkMode ? "Dark" : "Light"));
    }
    
    /**
     * 更新主题状态
     * 
     * 在配置变化时调用，包含性能优化的节流机制
     * 替换原有MainActivity中重复的主题检测逻辑
     * 
     * @param context Android上下文
     */
    public void updateThemeState(Context context) {
        long currentTime = System.currentTimeMillis();
        
        // 性能优化：节流机制，100ms内不重复检测
        if (currentTime - lastUpdateTime < UPDATE_THROTTLE_MS) {
            LogUtil.d(TAG, "Theme update throttled");
            return;
        }
        
        // 检测当前主题状态
        boolean newDarkMode = (context.getResources().getConfiguration().uiMode &
            Configuration.UI_MODE_NIGHT_MASK) == Configuration.UI_MODE_NIGHT_YES;
        
        // 只有状态真正变化时才更新
        if (newDarkMode != isDarkMode || currentColors == null) {
            isDarkMode = newDarkMode;
            currentColors = new ThemeColors(context, isDarkMode);
            lastUpdateTime = currentTime;
            
            // 通知LiveData观察者
            _themeState.postValue(isDarkMode);
            
            // 通知所有监听器
            notifyThemeChanged();
            
            LogUtil.d(TAG, "Theme updated to: " + (isDarkMode ? "Dark" : "Light"));
        } else {
            LogUtil.d(TAG, "Theme state unchanged: " + (isDarkMode ? "Dark" : "Light"));
        }
    }
    
    /**
     * 获取当前主题状态
     * 
     * @return true表示深色模式，false表示浅色模式
     */
    public boolean isDarkMode() {
        return isDarkMode;
    }
    
    /**
     * 获取当前主题颜色
     * 
     * @return 当前主题的颜色配置对象
     */
    public ThemeColors getCurrentColors() {
        return currentColors;
    }
    
    /**
     * 添加主题变化监听器
     * 
     * @param listener 主题变化监听器
     */
    public void addThemeChangeListener(ThemeChangeListener listener) {
        if (listener != null && !listeners.contains(listener)) {
            listeners.add(listener);
            
            // 立即通知当前状态
            if (isInitialized && currentColors != null) {
                listener.onThemeChanged(isDarkMode, currentColors);
            }
            
            LogUtil.d(TAG, "Theme change listener added, total: " + listeners.size());
        }
    }
    
    /**
     * 移除主题变化监听器
     * 
     * @param listener 要移除的监听器
     */
    public void removeThemeChangeListener(ThemeChangeListener listener) {
        if (listeners.remove(listener)) {
            LogUtil.d(TAG, "Theme change listener removed, remaining: " + listeners.size());
        }
    }
    
    /**
     * 通知所有监听器主题已变化
     */
    private void notifyThemeChanged() {
        if (currentColors != null) {
            LogUtil.d(TAG, "Notifying " + listeners.size() + " theme change listeners");
            
            for (ThemeChangeListener listener : listeners) {
                try {
                    listener.onThemeChanged(isDarkMode, currentColors);
                } catch (Exception e) {
                    LogUtil.e(TAG, "Error notifying theme change listener", e);
                }
            }
        }
    }
    
    /**
     * 清理资源
     * 
     * 在应用退出时调用，防止内存泄漏
     */
    public void cleanup() {
        listeners.clear();
        isInitialized = false;
        currentColors = null;
        LogUtil.d(TAG, "ThemeManager cleaned up");
    }
    
    /**
     * 获取初始化状态
     * 
     * @return 是否已初始化
     */
    public boolean isInitialized() {
        return isInitialized;
    }
}
