package com.autolink.sbjk.ui.widget;

import android.content.Context;
import android.graphics.SurfaceTexture;
import android.util.AttributeSet;
import android.view.Surface;
import android.view.TextureView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.autolink.sbjk.common.util.LogUtil;

/**
 * 自定义相机预览TextureView
 * 封装相机预览逻辑，遵循MVVM架构模式
 */
public class CameraTextureView extends TextureView implements TextureView.SurfaceTextureListener {
    
    private static final String TAG = "CameraTextureView";
    
    private String cameraId;
    private CameraPreviewListener previewListener;
    private boolean isConfigurationChanging = false;
    
    /**
     * 相机预览监听器接口
     */
    public interface CameraPreviewListener {
        void onPreviewSurfaceAvailable(String cameraId, Surface surface);
        void onPreviewSurfaceChanged(String cameraId, Surface surface);
        void onPreviewSurfaceDestroyed(String cameraId);
        void onPreviewSurfaceUpdated(String cameraId);
    }
    
    public CameraTextureView(Context context) {
        super(context);
        init();
    }
    
    public CameraTextureView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }
    
    public CameraTextureView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }
    
    private void init() {
        setSurfaceTextureListener(this);
        LogUtil.d(TAG, "CameraTextureView initialized");
    }
    
    /**
     * 设置相机ID
     */
    public void setCameraId(String cameraId) {
        this.cameraId = cameraId;
        LogUtil.d(TAG, "Camera ID set to: " + cameraId);
    }
    
    /**
     * 获取相机ID
     */
    public String getCameraId() {
        return cameraId;
    }
    
    /**
     * 设置预览监听器
     */
    public void setPreviewListener(CameraPreviewListener listener) {
        this.previewListener = listener;
    }
    
    /**
     * 设置配置变化状态
     */
    public void setConfigurationChanging(boolean changing) {
        this.isConfigurationChanging = changing;
        LogUtil.d(TAG, "Configuration changing: " + changing + " for camera " + cameraId);
    }
    
    @Override
    public void onSurfaceTextureAvailable(@NonNull SurfaceTexture surface, int width, int height) {
        LogUtil.d(TAG, "Surface texture available for camera " + cameraId + 
                  ", size: " + width + "x" + height);
        
        if (previewListener != null && cameraId != null) {
            Surface previewSurface = new Surface(surface);
            previewListener.onPreviewSurfaceAvailable(cameraId, previewSurface);
        } else {
            LogUtil.w(TAG, "Cannot notify surface available: listener=" + 
                      (previewListener != null) + ", cameraId=" + cameraId);
        }
    }
    
    @Override
    public void onSurfaceTextureSizeChanged(@NonNull SurfaceTexture surface, int width, int height) {
        LogUtil.d(TAG, "Surface texture size changed for camera " + cameraId + 
                  ", new size: " + width + "x" + height);
        
        if (previewListener != null && cameraId != null) {
            Surface previewSurface = new Surface(surface);
            previewListener.onPreviewSurfaceChanged(cameraId, previewSurface);
        }
    }
    
    @Override
    public boolean onSurfaceTextureDestroyed(@NonNull SurfaceTexture surface) {
        LogUtil.d(TAG, "Surface texture destroyed for camera " + cameraId + 
                  ", configurationChanging: " + isConfigurationChanging);
        
        // 在配置变化时不通知销毁，避免中断录制
        if (!isConfigurationChanging && previewListener != null && cameraId != null) {
            previewListener.onPreviewSurfaceDestroyed(cameraId);
        }
        
        return true;
    }
    
    @Override
    public void onSurfaceTextureUpdated(@NonNull SurfaceTexture surface) {
        // 预览帧更新，通常不需要处理，但可以用于性能监控
        if (previewListener != null && cameraId != null) {
            previewListener.onPreviewSurfaceUpdated(cameraId);
        }
    }
    
    /**
     * 获取预览Surface
     */
    @Nullable
    public Surface getPreviewSurface() {
        SurfaceTexture surfaceTexture = getSurfaceTexture();
        if (surfaceTexture != null) {
            return new Surface(surfaceTexture);
        }
        return null;
    }
    
    /**
     * 检查预览是否可用
     */
    public boolean isPreviewAvailable() {
        return isAvailable() && getSurfaceTexture() != null;
    }
    
    /**
     * 设置预览尺寸（保持宽高比）
     */
    public void setPreviewSize(int width, int height) {
        if (width <= 0 || height <= 0) {
            LogUtil.w(TAG, "Invalid preview size: " + width + "x" + height);
            return;
        }
        
        post(() -> {
            int viewWidth = getWidth();
            int viewHeight = getHeight();
            
            if (viewWidth == 0 || viewHeight == 0) {
                LogUtil.w(TAG, "View size not ready, deferring preview size setting");
                return;
            }
            
            // 计算保持宽高比的尺寸
            float aspectRatio = (float) width / height;
            float viewAspectRatio = (float) viewWidth / viewHeight;
            
            int newWidth, newHeight;
            if (aspectRatio > viewAspectRatio) {
                newWidth = viewWidth;
                newHeight = (int) (viewWidth / aspectRatio);
            } else {
                newWidth = (int) (viewHeight * aspectRatio);
                newHeight = viewHeight;
            }
            
            // 居中显示
            int left = (viewWidth - newWidth) / 2;
            int top = (viewHeight - newHeight) / 2;
            
            setTranslationX(left);
            setTranslationY(top);
            
            LogUtil.d(TAG, "Preview size set for camera " + cameraId + 
                      ": " + newWidth + "x" + newHeight + 
                      " (original: " + width + "x" + height + ")");
        });
    }
    
    /**
     * 重置预览变换
     */
    public void resetPreviewTransform() {
        setTranslationX(0);
        setTranslationY(0);
        setScaleX(1.0f);
        setScaleY(1.0f);
        setRotation(0);
        
        LogUtil.d(TAG, "Preview transform reset for camera " + cameraId);
    }
    
    /**
     * 设置预览旋转
     */
    public void setPreviewRotation(int rotation) {
        setRotation(rotation);
        LogUtil.d(TAG, "Preview rotation set to " + rotation + " for camera " + cameraId);
    }
    
    /**
     * 获取预览状态信息
     */
    public String getPreviewStatus() {
        return "CameraTextureView[" + cameraId + "]: " +
               "available=" + isAvailable() + 
               ", size=" + getWidth() + "x" + getHeight() +
               ", surfaceTexture=" + (getSurfaceTexture() != null) +
               ", listener=" + (previewListener != null);
    }
    
    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        
        // 清理资源
        previewListener = null;
        
        LogUtil.d(TAG, "CameraTextureView detached from window for camera " + cameraId);
    }
    
    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        LogUtil.d(TAG, "CameraTextureView attached to window for camera " + cameraId);
    }
}
