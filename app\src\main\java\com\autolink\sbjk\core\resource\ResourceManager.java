package com.autolink.sbjk.core.resource;

import android.app.ActivityManager;
import android.content.Context;
import android.os.Debug;
import android.os.Handler;
import android.os.Looper;
import android.os.StatFs;

import com.autolink.sbjk.common.util.LogUtil;
import com.autolink.sbjk.common.constant.CameraConstants;

import java.io.File;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 资源管理器
 * 监控内存、存储空间等系统资源
 */
public class ResourceManager {
    
    private static final String TAG = "ResourceManager";
    private static volatile ResourceManager instance;
    
    // 资源阈值常量
    private static final long MIN_AVAILABLE_MEMORY_MB = 100; // 最小可用内存100MB
    private static final long MIN_AVAILABLE_STORAGE_GB = 1;  // 最小可用存储1GB
    private static final long MONITORING_INTERVAL_MS = 30000; // 监控间隔30秒
    
    private final Context context;
    private final ActivityManager activityManager;
    private final Handler monitoringHandler;
    private final AtomicBoolean isMonitoring;
    private final Runnable monitoringRunnable;
    
    // 资源状态
    private volatile boolean isMemoryLow = false;
    private volatile boolean isStorageLow = false;
    private volatile long lastMemoryCheck = 0;
    private volatile long lastStorageCheck = 0;
    
    // 监听器接口
    public interface ResourceListener {
        void onMemoryLow(long availableMemoryMB);
        void onStorageLow(long availableStorageGB);
        void onResourceRecovered();
    }
    
    private ResourceListener resourceListener;
    
    private ResourceManager(Context context) {
        this.context = context.getApplicationContext();
        this.activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        this.monitoringHandler = new Handler(Looper.getMainLooper());
        this.isMonitoring = new AtomicBoolean(false);
        
        this.monitoringRunnable = new Runnable() {
            @Override
            public void run() {
                if (isMonitoring.get()) {
                    checkResources();
                    monitoringHandler.postDelayed(this, MONITORING_INTERVAL_MS);
                }
            }
        };
        
        LogUtil.d(TAG, "ResourceManager initialized");
    }
    
    public static ResourceManager getInstance(Context context) {
        if (instance == null) {
            synchronized (ResourceManager.class) {
                if (instance == null) {
                    instance = new ResourceManager(context);
                }
            }
        }
        return instance;
    }
    
    /**
     * 设置资源监听器
     */
    public void setResourceListener(ResourceListener listener) {
        this.resourceListener = listener;
    }
    
    /**
     * 开始资源监控
     */
    public void startMonitoring() {
        if (isMonitoring.compareAndSet(false, true)) {
            LogUtil.i(TAG, "Starting resource monitoring");
            monitoringHandler.post(monitoringRunnable);
        }
    }
    
    /**
     * 停止资源监控
     */
    public void stopMonitoring() {
        if (isMonitoring.compareAndSet(true, false)) {
            LogUtil.i(TAG, "Stopping resource monitoring");
            monitoringHandler.removeCallbacks(monitoringRunnable);
        }
    }
    
    /**
     * 检查所有资源状态
     */
    private void checkResources() {
        try {
            checkMemoryStatus();
            checkStorageStatus();
            
            // 检查是否从低资源状态恢复
            if (!isMemoryLow && !isStorageLow && resourceListener != null) {
                resourceListener.onResourceRecovered();
            }
            
        } catch (Exception e) {
            LogUtil.e(TAG, "Exception during resource check", e);
        }
    }
    
    /**
     * 检查内存状态
     */
    private void checkMemoryStatus() {
        try {
            ActivityManager.MemoryInfo memoryInfo = new ActivityManager.MemoryInfo();
            activityManager.getMemoryInfo(memoryInfo);
            
            long availableMemoryMB = memoryInfo.availMem / (1024 * 1024);
            lastMemoryCheck = availableMemoryMB;
            
            boolean wasMemoryLow = isMemoryLow;
            isMemoryLow = availableMemoryMB < MIN_AVAILABLE_MEMORY_MB;
            
            if (isMemoryLow && !wasMemoryLow) {
                LogUtil.w(TAG, "Memory low detected: " + availableMemoryMB + "MB available");
                if (resourceListener != null) {
                    resourceListener.onMemoryLow(availableMemoryMB);
                }
            }
            
            LogUtil.d(TAG, "Memory check: " + availableMemoryMB + "MB available, low=" + isMemoryLow);
            
        } catch (Exception e) {
            LogUtil.e(TAG, "Exception checking memory status", e);
        }
    }
    
    /**
     * 检查存储空间状态
     */
    private void checkStorageStatus() {
        try {
            File recordingDir = new File(CameraConstants.DEFAULT_RECORD_PATH);
            if (!recordingDir.exists()) {
                recordingDir.mkdirs();
            }
            
            StatFs statFs = new StatFs(recordingDir.getAbsolutePath());
            long availableBytes = statFs.getAvailableBytes();
            long availableStorageGB = availableBytes / (1024 * 1024 * 1024);
            lastStorageCheck = availableStorageGB;
            
            boolean wasStorageLow = isStorageLow;
            isStorageLow = availableStorageGB < MIN_AVAILABLE_STORAGE_GB;
            
            if (isStorageLow && !wasStorageLow) {
                LogUtil.w(TAG, "Storage low detected: " + availableStorageGB + "GB available");
                if (resourceListener != null) {
                    resourceListener.onStorageLow(availableStorageGB);
                }
            }
            
            LogUtil.d(TAG, "Storage check: " + availableStorageGB + "GB available, low=" + isStorageLow);
            
        } catch (Exception e) {
            LogUtil.e(TAG, "Exception checking storage status", e);
        }
    }
    
    /**
     * 获取当前内存使用情况
     */
    public MemoryInfo getCurrentMemoryInfo() {
        try {
            ActivityManager.MemoryInfo memoryInfo = new ActivityManager.MemoryInfo();
            activityManager.getMemoryInfo(memoryInfo);
            
            Debug.MemoryInfo debugMemoryInfo = new Debug.MemoryInfo();
            Debug.getMemoryInfo(debugMemoryInfo);
            
            return new MemoryInfo(
                memoryInfo.totalMem / (1024 * 1024),
                memoryInfo.availMem / (1024 * 1024),
                debugMemoryInfo.getTotalPss() / 1024,
                isMemoryLow
            );
            
        } catch (Exception e) {
            LogUtil.e(TAG, "Exception getting memory info", e);
            return new MemoryInfo(0, 0, 0, true);
        }
    }
    
    /**
     * 获取当前存储空间信息
     */
    public StorageInfo getCurrentStorageInfo() {
        try {
            File recordingDir = new File(CameraConstants.DEFAULT_RECORD_PATH);
            StatFs statFs = new StatFs(recordingDir.getAbsolutePath());
            
            long totalBytes = statFs.getTotalBytes();
            long availableBytes = statFs.getAvailableBytes();
            
            return new StorageInfo(
                totalBytes / (1024 * 1024 * 1024),
                availableBytes / (1024 * 1024 * 1024),
                isStorageLow
            );
            
        } catch (Exception e) {
            LogUtil.e(TAG, "Exception getting storage info", e);
            return new StorageInfo(0, 0, true);
        }
    }
    
    /**
     * 检查是否可以开始录制
     */
    public boolean canStartRecording() {
        return !isMemoryLow && !isStorageLow;
    }
    
    /**
     * 获取资源状态摘要
     */
    public String getResourceSummary() {
        MemoryInfo memInfo = getCurrentMemoryInfo();
        StorageInfo storageInfo = getCurrentStorageInfo();
        
        return "Resource Status: " +
               "Memory=" + memInfo.availableMemoryMB + "MB/" + memInfo.totalMemoryMB + "MB" +
               ", Storage=" + storageInfo.availableStorageGB + "GB/" + storageInfo.totalStorageGB + "GB" +
               ", CanRecord=" + canStartRecording();
    }
    
    /**
     * 释放资源
     */
    public void release() {
        stopMonitoring();
        resourceListener = null;
        LogUtil.d(TAG, "ResourceManager released");
    }
    
    /**
     * 内存信息数据类
     */
    public static class MemoryInfo {
        public final long totalMemoryMB;
        public final long availableMemoryMB;
        public final long usedMemoryMB;
        public final boolean isLow;
        
        public MemoryInfo(long totalMemoryMB, long availableMemoryMB, long usedMemoryMB, boolean isLow) {
            this.totalMemoryMB = totalMemoryMB;
            this.availableMemoryMB = availableMemoryMB;
            this.usedMemoryMB = usedMemoryMB;
            this.isLow = isLow;
        }
    }
    
    /**
     * 存储信息数据类
     */
    public static class StorageInfo {
        public final long totalStorageGB;
        public final long availableStorageGB;
        public final boolean isLow;
        
        public StorageInfo(long totalStorageGB, long availableStorageGB, boolean isLow) {
            this.totalStorageGB = totalStorageGB;
            this.availableStorageGB = availableStorageGB;
            this.isLow = isLow;
        }
    }
}
