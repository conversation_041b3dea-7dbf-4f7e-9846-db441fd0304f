# ThemeManager 架构设计

## 核心类设计

```java
package com.autolink.sbjk.common.theme;

import android.content.Context;
import android.content.res.Configuration;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 统一的主题管理器
 * 职责：
 * 1. 统一管理主题状态检测和缓存
 * 2. 提供主题变化通知机制
 * 3. 集中管理颜色资源获取
 * 4. 优化主题检测性能
 */
public class ThemeManager {
    
    private static final String TAG = "ThemeManager";
    private static volatile ThemeManager instance;
    
    // 主题状态
    private boolean isDarkMode = false;
    private boolean isInitialized = false;
    
    // 观察者模式
    private final MutableLiveData<Boolean> _themeState = new MutableLiveData<>();
    public final LiveData<Boolean> themeState = _themeState;
    
    // 主题变化监听器列表
    private final CopyOnWriteArrayList<ThemeChangeListener> listeners = new CopyOnWriteArrayList<>();
    
    // 颜色资源缓存
    private ThemeColors currentColors;
    
    /**
     * 主题变化监听器接口
     */
    public interface ThemeChangeListener {
        void onThemeChanged(boolean isDarkMode, ThemeColors colors);
    }
    
    /**
     * 主题颜色资源类
     */
    public static class ThemeColors {
        public final int textPrimary;
        public final int textSecondary;
        public final int backgroundPrimary;
        public final int backgroundSecondary;
        public final int containerBackground;
        public final int buttonTextSelected;
        public final int buttonTextUnselected;
        public final int statusBarColor;
        
        public ThemeColors(Context context, boolean isDarkMode) {
            if (isDarkMode) {
                // 夜间模式颜色
                textPrimary = Color.WHITE;
                textSecondary = Color.parseColor("#CCCCCC");
                backgroundPrimary = Color.BLACK;
                backgroundSecondary = Color.BLACK;
                containerBackground = Color.parseColor("#1A1A1A");
                buttonTextSelected = Color.WHITE;
                buttonTextUnselected = Color.parseColor("#808080");
                statusBarColor = Color.BLACK;
            } else {
                // 日间模式颜色
                textPrimary = Color.BLACK;
                textSecondary = Color.parseColor("#666666");
                backgroundPrimary = Color.parseColor("#DEE2E5");
                backgroundSecondary = Color.parseColor("#DEE2E5");
                containerBackground = Color.parseColor("#F5F5F5");
                buttonTextSelected = Color.BLACK;
                buttonTextUnselected = Color.parseColor("#808080");
                statusBarColor = Color.parseColor("#DEE2E5");
            }
        }
    }
    
    // 单例模式
    public static ThemeManager getInstance() {
        if (instance == null) {
            synchronized (ThemeManager.class) {
                if (instance == null) {
                    instance = new ThemeManager();
                }
            }
        }
        return instance;
    }
    
    private ThemeManager() {
        // 私有构造函数
    }
    
    /**
     * 初始化主题管理器
     */
    public void initialize(Context context) {
        if (isInitialized) {
            return;
        }
        
        updateThemeState(context);
        isInitialized = true;
        LogUtil.d(TAG, "ThemeManager initialized with theme: " + (isDarkMode ? "Dark" : "Light"));
    }
    
    /**
     * 更新主题状态（在配置变化时调用）
     */
    public void updateThemeState(Context context) {
        boolean newDarkMode = (context.getResources().getConfiguration().uiMode &
            Configuration.UI_MODE_NIGHT_MASK) == Configuration.UI_MODE_NIGHT_YES;
        
        if (newDarkMode != isDarkMode || currentColors == null) {
            isDarkMode = newDarkMode;
            currentColors = new ThemeColors(context, isDarkMode);
            
            // 通知观察者
            _themeState.postValue(isDarkMode);
            notifyThemeChanged();
            
            LogUtil.d(TAG, "Theme updated to: " + (isDarkMode ? "Dark" : "Light"));
        }
    }
    
    /**
     * 获取当前主题状态
     */
    public boolean isDarkMode() {
        return isDarkMode;
    }
    
    /**
     * 获取当前主题颜色
     */
    public ThemeColors getCurrentColors() {
        return currentColors;
    }
    
    /**
     * 添加主题变化监听器
     */
    public void addThemeChangeListener(ThemeChangeListener listener) {
        if (listener != null && !listeners.contains(listener)) {
            listeners.add(listener);
            
            // 立即通知当前状态
            if (isInitialized && currentColors != null) {
                listener.onThemeChanged(isDarkMode, currentColors);
            }
        }
    }
    
    /**
     * 移除主题变化监听器
     */
    public void removeThemeChangeListener(ThemeChangeListener listener) {
        listeners.remove(listener);
    }
    
    /**
     * 通知所有监听器主题已变化
     */
    private void notifyThemeChanged() {
        if (currentColors != null) {
            for (ThemeChangeListener listener : listeners) {
                try {
                    listener.onThemeChanged(isDarkMode, currentColors);
                } catch (Exception e) {
                    LogUtil.e(TAG, "Error notifying theme change listener", e);
                }
            }
        }
    }
    
    /**
     * 清理资源
     */
    public void cleanup() {
        listeners.clear();
        isInitialized = false;
        LogUtil.d(TAG, "ThemeManager cleaned up");
    }
}
```

## 主题应用器设计

```java
package com.autolink.sbjk.common.theme;

import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import androidx.cardview.widget.CardView;

/**
 * 主题应用器 - 统一的UI组件主题应用逻辑
 */
public class ThemeApplier {
    
    private static final String TAG = "ThemeApplier";
    
    /**
     * 应用主题到TextView
     */
    public static void applyToTextView(TextView textView, ThemeManager.ThemeColors colors, boolean isPrimary) {
        if (textView != null) {
            textView.setTextColor(isPrimary ? colors.textPrimary : colors.textSecondary);
        }
    }
    
    /**
     * 应用主题到按钮（选中状态）
     */
    public static void applyToButton(Button button, ThemeManager.ThemeColors colors, boolean isSelected) {
        if (button != null) {
            button.setTextColor(isSelected ? colors.buttonTextSelected : colors.buttonTextUnselected);
        }
    }
    
    /**
     * 应用主题到背景View
     */
    public static void applyToBackground(View view, ThemeManager.ThemeColors colors, boolean isPrimary) {
        if (view != null) {
            view.setBackgroundColor(isPrimary ? colors.backgroundPrimary : colors.backgroundSecondary);
        }
    }
    
    /**
     * 应用主题到CardView容器
     */
    public static void applyToCardView(CardView cardView, ThemeManager.ThemeColors colors) {
        if (cardView != null) {
            cardView.setCardBackgroundColor(colors.containerBackground);
        }
    }
    
    /**
     * 应用主题到页面按钮（特殊逻辑）
     */
    public static void applyToPageButtons(Button activeButton, Button inactiveButton, ThemeManager.ThemeColors colors) {
        if (activeButton != null && inactiveButton != null) {
            activeButton.setSelected(true);
            inactiveButton.setSelected(false);
            
            activeButton.setTextColor(colors.buttonTextSelected);
            inactiveButton.setTextColor(colors.buttonTextUnselected);
        }
    }
}
```
