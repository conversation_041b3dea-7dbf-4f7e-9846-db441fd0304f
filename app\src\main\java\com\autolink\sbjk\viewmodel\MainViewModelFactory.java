package com.autolink.sbjk.viewmodel;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider;

import com.autolink.sbjk.di.DIContainer;
import com.autolink.sbjk.model.repository.CameraRepository;
import com.autolink.sbjk.model.repository.RecordingRepository;
import com.autolink.sbjk.model.repository.StorageRepository;
import com.autolink.sbjk.common.util.LogUtil;

/**
 * MainViewModel工厂类
 * 确保ViewModel在配置更改时正确保持状态
 */
public class MainViewModelFactory implements ViewModelProvider.Factory {
    
    private static final String TAG = "MainViewModelFactory";
    
    private final Context context;
    
    public MainViewModelFactory(@NonNull Context context) {
        this.context = context.getApplicationContext();
    }
    
    @NonNull
    @Override
    @SuppressWarnings("unchecked")
    public <T extends ViewModel> T create(@NonNull Class<T> modelClass) {
        if (modelClass.isAssignableFrom(MainViewModel.class)) {
            try {
                // 使用依赖注入获取Repository实例
                CameraRepository cameraRepository = DIContainer.provideCameraRepository(context);
                RecordingRepository recordingRepository = DIContainer.provideRecordingRepository(context);
                StorageRepository storageRepository = DIContainer.provideStorageRepository(context);

                MainViewModel viewModel = new MainViewModel(cameraRepository, recordingRepository, storageRepository);
                LogUtil.d(TAG, "MainViewModel created through ViewModelFactory with DI");
                return (T) viewModel;
                
            } catch (Exception e) {
                LogUtil.e(TAG, "Failed to create MainViewModel with DI in factory", e);
                // 降级方案：使用Application构造函数
                MainViewModel viewModel = new MainViewModel(
                    (android.app.Application) context.getApplicationContext()
                );
                LogUtil.d(TAG, "MainViewModel created through ViewModelFactory with Application fallback");
                return (T) viewModel;
            }
        }
        
        throw new IllegalArgumentException("Unknown ViewModel class: " + modelClass.getName());
    }
}
