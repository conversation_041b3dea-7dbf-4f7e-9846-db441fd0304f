<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- 相机预览 -->
    <SurfaceView
        android:id="@+id/camera_surface_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <!-- 控制按钮 -->
    <Button
        android:id="@+id/btnStartStop"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="开始录制"
        android:padding="16dp"
        android:layout_margin="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout> 