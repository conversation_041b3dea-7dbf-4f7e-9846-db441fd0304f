package com.autolink.sbjk.ui;

import android.app.AlertDialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.view.ContextThemeWrapper;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.NumberPicker;
import android.widget.TextView;
import androidx.core.content.ContextCompat;
import java.lang.reflect.Field;
import com.autolink.sbjk.R;

import com.autolink.sbjk.common.util.LogUtil;

import java.util.Calendar;

/**
 * 时间选择器管理器
 * 管理月、日、时三个按钮的点击事件和弹出对话框
 */
public class TimePickerManager {

    private static final String TAG = "TimePickerManager";

    // 时间选择回调接口
    public interface OnTimeSelectedListener {
        void onTimeSelected(int month, int day, int hour);
    }

    private OnTimeSelectedListener timeSelectedListener;
    private Context context;

    // 按钮控件
    private Button btnMonthPicker;
    private Button btnDayPicker;
    private Button btnHourPicker;

    // 当前选中的值
    private int selectedMonth;
    private int selectedDay;
    private int selectedHour;

    // 月份天数映射
    private static final int[] DAYS_IN_MONTH = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};

    public TimePickerManager(Context context) {
        this.context = context;
    }
    
    /**
     * 设置时间选择监听器
     */
    public void setOnTimeSelectedListener(OnTimeSelectedListener listener) {
        this.timeSelectedListener = listener;
    }

    /**
     * 初始化时间选择器按钮
     */
    public void initTimePickers(Button btnMonthPicker, Button btnDayPicker, Button btnHourPicker) {
        this.btnMonthPicker = btnMonthPicker;
        this.btnDayPicker = btnDayPicker;
        this.btnHourPicker = btnHourPicker;

        setupTimePickerButtons();

        // 设置为当前时间
        setCurrentTime();

        LogUtil.d(TAG, "时间选择器按钮初始化完成");
    }
    
    /**
     * 设置时间选择器按钮
     */
    private void setupTimePickerButtons() {
        // 设置月份选择按钮
        if (btnMonthPicker != null) {
            btnMonthPicker.setOnClickListener(v -> showTimePickerDialog());
        }

        // 设置日期选择按钮
        if (btnDayPicker != null) {
            btnDayPicker.setOnClickListener(v -> showTimePickerDialog());
        }

        // 设置小时选择按钮
        if (btnHourPicker != null) {
            btnHourPicker.setOnClickListener(v -> showTimePickerDialog());
        }
    }

    /**
     * 显示时间选择对话框（包含月、日、时三个NumberPicker）
     */
    private void showTimePickerDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        builder.setTitle("选择时间");

        LinearLayout mainLayout = new LinearLayout(context);
        mainLayout.setOrientation(LinearLayout.VERTICAL);
        mainLayout.setPadding(50, 50, 50, 50);

        // 创建水平布局来放置三个NumberPicker
        LinearLayout pickerLayout = new LinearLayout(context);
        pickerLayout.setOrientation(LinearLayout.HORIZONTAL);
        pickerLayout.setGravity(Gravity.CENTER);
        pickerLayout.setPadding(20, 20, 20, 20);

        // 创建月份NumberPicker
        LinearLayout monthContainer = new LinearLayout(context);
        monthContainer.setOrientation(LinearLayout.VERTICAL);
        monthContainer.setGravity(Gravity.CENTER);
        monthContainer.setPadding(15, 10, 15, 10);
        monthContainer.setMinimumWidth(120);

        TextView monthLabel = new TextView(context);
        monthLabel.setText("月份");
        monthLabel.setGravity(Gravity.CENTER);
        monthLabel.setTextSize(18);
        // 使用对话框专用标签文字颜色，支持日夜模式切换
        monthLabel.setTextColor(ContextCompat.getColor(context, R.color.dialog_label_text_color));
        monthLabel.setPadding(0, 0, 0, 15);

        // 使用自定义样式创建NumberPicker
        ContextThemeWrapper themedContext = new ContextThemeWrapper(context, R.style.NumberPickerStyle);
        NumberPicker monthPicker = new NumberPicker(themedContext);
        monthPicker.setMinValue(0);  // 0表示"全部"
        monthPicker.setMaxValue(12);
        // 如果当前选择是"全部"，设置为0，否则设置为实际月份
        monthPicker.setValue(selectedMonth == -1 ? 0 : selectedMonth);
        monthPicker.setWrapSelectorWheel(true);

        // 设置月份显示格式，包含"全部"选项
        String[] monthDisplayValues = new String[13];
        monthDisplayValues[0] = "全部";
        for (int i = 1; i <= 12; i++) {
            monthDisplayValues[i] = String.format("%d", i);
        }
        monthPicker.setDisplayedValues(monthDisplayValues);

        monthContainer.addView(monthLabel);
        monthContainer.addView(monthPicker);

        // 调大月份NumberPicker字体30%
        adjustNumberPickerTextSize(monthPicker, 1.3f);

        // 添加值变化监听器，确保滚动时字体大小保持一致
        addTextSizeListener(monthPicker, 1.3f);

        // 创建日期NumberPicker
        LinearLayout dayContainer = new LinearLayout(context);
        dayContainer.setOrientation(LinearLayout.VERTICAL);
        dayContainer.setGravity(Gravity.CENTER);
        dayContainer.setPadding(15, 10, 15, 10);
        dayContainer.setMinimumWidth(120);

        TextView dayLabel = new TextView(context);
        dayLabel.setText("日期");
        dayLabel.setGravity(Gravity.CENTER);
        dayLabel.setTextSize(18);
        // 使用对话框专用标签文字颜色，支持日夜模式切换
        dayLabel.setTextColor(ContextCompat.getColor(context, R.color.dialog_label_text_color));
        dayLabel.setPadding(0, 0, 0, 15);

        // 使用自定义样式创建NumberPicker
        NumberPicker dayPicker = new NumberPicker(themedContext);
        dayPicker.setMinValue(0); // 0表示"全部"
        int maxDay = getDaysInMonth(selectedMonth);
        dayPicker.setMaxValue(maxDay);
        // 设置当前值，如果selectedDay为-1表示全部，则设置为0
        dayPicker.setValue(selectedDay == -1 ? 0 : Math.min(selectedDay, maxDay));
        dayPicker.setWrapSelectorWheel(true);

        // 设置日期显示格式（包含"全部"选项）
        updateDayPickerValues(dayPicker, maxDay);

        dayContainer.addView(dayLabel);
        dayContainer.addView(dayPicker);

        // 调大日期NumberPicker字体30%
        adjustNumberPickerTextSize(dayPicker, 1.3f);

        // 添加值变化监听器，确保滚动时字体大小保持一致
        addTextSizeListener(dayPicker, 1.3f);

        // 创建小时NumberPicker
        LinearLayout hourContainer = new LinearLayout(context);
        hourContainer.setOrientation(LinearLayout.VERTICAL);
        hourContainer.setGravity(Gravity.CENTER);
        hourContainer.setPadding(15, 10, 15, 10);
        hourContainer.setMinimumWidth(120);

        TextView hourLabel = new TextView(context);
        hourLabel.setText("小时");
        hourLabel.setGravity(Gravity.CENTER);
        hourLabel.setTextSize(18);
        // 使用对话框专用标签文字颜色，支持日夜模式切换
        hourLabel.setTextColor(ContextCompat.getColor(context, R.color.dialog_label_text_color));
        hourLabel.setPadding(0, 0, 0, 15);

        // 使用自定义样式创建NumberPicker
        NumberPicker hourPicker = new NumberPicker(themedContext);
        hourPicker.setMinValue(0); // 0表示"全部"
        hourPicker.setMaxValue(24); // 24个选项：全部 + 0-23时
        // 设置当前值，如果selectedHour为-1表示全部，则设置为0
        hourPicker.setValue(selectedHour == -1 ? 0 : selectedHour + 1);
        hourPicker.setWrapSelectorWheel(true);

        // 设置小时显示格式（包含"全部"选项）
        String[] hourDisplayValues = new String[25]; // 25个选项：全部 + 0-23时
        hourDisplayValues[0] = "全部";
        for (int i = 1; i <= 24; i++) {
            hourDisplayValues[i] = String.format("%02d", i - 1);
        }
        hourPicker.setDisplayedValues(hourDisplayValues);

        hourContainer.addView(hourLabel);
        hourContainer.addView(hourPicker);

        // 调大小时NumberPicker字体30%
        adjustNumberPickerTextSize(hourPicker, 1.3f);

        // 添加值变化监听器，确保滚动时字体大小保持一致
        addTextSizeListener(hourPicker, 1.3f);

        // 设置月份变化监听器，当月份改变时更新日期范围
        // 注意：这会覆盖之前设置的字体监听器，所以需要在这里也处理字体大小
        monthPicker.setOnValueChangedListener((picker, oldVal, newVal) -> {
            try {
                int newMaxDay = getDaysInMonth(newVal);

                // 先清除displayedValues，避免数组越界
                dayPicker.setDisplayedValues(null);

                // 然后设置新的最大值
                dayPicker.setMaxValue(newMaxDay);

                // 最后设置新的displayedValues
                updateDayPickerValues(dayPicker, newMaxDay);

                // 如果当前选中的日期超出了新月份的范围，调整到最大值
                // 注意：0表示"全部"，所以只有当值大于0且超出范围时才调整
                int currentDayValue = dayPicker.getValue();
                if (currentDayValue > 0 && currentDayValue > newMaxDay) {
                    dayPicker.setValue(newMaxDay);
                }

                // 重新设置月份NumberPicker的字体大小
                picker.postDelayed(() -> {
                    setNumberPickerTextSize(picker, 1.3f);
                    // 同时更新日期NumberPicker的字体大小
                    setNumberPickerTextSize(dayPicker, 1.3f);
                }, 50);

            } catch (Exception e) {
                LogUtil.e(TAG, "月份变化监听器执行失败", e);
            }
        });

        // 添加到水平布局，并在之间添加分隔线
        pickerLayout.addView(monthContainer);

        // 添加第一个分隔线
        View separator1 = new View(context);
        LinearLayout.LayoutParams separatorParams1 = new LinearLayout.LayoutParams(2, 200);
        separatorParams1.setMargins(20, 30, 20, 30);
        separator1.setLayoutParams(separatorParams1);
        // 使用适配颜色资源，支持日夜模式切换
        separator1.setBackgroundColor(ContextCompat.getColor(context, R.color.separator_line_color));
        pickerLayout.addView(separator1);

        pickerLayout.addView(dayContainer);

        // 添加第二个分隔线
        View separator2 = new View(context);
        LinearLayout.LayoutParams separatorParams2 = new LinearLayout.LayoutParams(2, 200);
        separatorParams2.setMargins(20, 30, 20, 30);
        separator2.setLayoutParams(separatorParams2);
        // 使用适配颜色资源，支持日夜模式切换
        separator2.setBackgroundColor(ContextCompat.getColor(context, R.color.separator_line_color));
        pickerLayout.addView(separator2);

        pickerLayout.addView(hourContainer);

        mainLayout.addView(pickerLayout);
        builder.setView(mainLayout);

        builder.setPositiveButton("确定", (dialog, which) -> {
            selectedMonth = monthPicker.getValue();
            // 日期：0表示"全部"，转换为-1；其他值保持不变
            selectedDay = dayPicker.getValue() == 0 ? -1 : dayPicker.getValue();
            // 小时：0表示"全部"，转换为-1；其他值需要减1（因为显示时加了1）
            selectedHour = hourPicker.getValue() == 0 ? -1 : hourPicker.getValue() - 1;
            updateButtonText();
            notifyTimeSelected();
        });

        builder.setNegativeButton("取消", null);
        AlertDialog dialog = builder.create();

        // 设置对话框圆角背景（与视频播放器一致的8dp圆角）
        setDialogRoundedBackground(dialog);

        dialog.show();
    }

    /**
     * 更新日期NumberPicker的显示值（包含"全部"选项）
     */
    private void updateDayPickerValues(NumberPicker dayPicker, int maxDay) {
        String[] dayDisplayValues = new String[maxDay + 1]; // +1 for "全部" option
        dayDisplayValues[0] = "全部";
        for (int i = 1; i <= maxDay; i++) {
            dayDisplayValues[i] = String.format("%d", i);
        }
        dayPicker.setDisplayedValues(dayDisplayValues);
    }

    /**
     * 调整NumberPicker的字体大小（包括所有子视图）
     * 【修复】只调整字体大小，颜色由样式文件R.style.NumberPickerStyle控制，避免双重设置冲突
     * @param numberPicker NumberPicker控件
     * @param scaleFactor 缩放因子（1.3f表示放大30%）
     */
    private void adjustNumberPickerTextSize(NumberPicker numberPicker, float scaleFactor) {
        try {
            // 设置NumberPicker的文字大小
            setNumberPickerTextSize(numberPicker, scaleFactor);

            // 延迟执行，确保NumberPicker完全初始化后再设置
            numberPicker.post(() -> {
                setNumberPickerTextSize(numberPicker, scaleFactor);

                // 再次延迟执行，处理滚动后的字体重置问题
                numberPicker.postDelayed(() -> {
                    setNumberPickerTextSize(numberPicker, scaleFactor);
                }, 100);
            });

        } catch (Exception e) {
            LogUtil.w(TAG, "调整NumberPicker字体大小失败", e);
        }
    }

    /**
     * 设置NumberPicker的文字大小（包括所有相关的TextView）
     * 【修复】只设置字体大小，不设置颜色，让样式文件中的颜色设置生效
     */
    private void setNumberPickerTextSize(NumberPicker numberPicker, float scaleFactor) {
        try {
            // 获取默认字体大小（样式文件中已经设置为18sp，这里直接使用）
            float baseTextSize = 18f; // 样式文件中的字体大小
            // 【修复】计算实际目标字体大小
            float targetTextSize = baseTextSize * scaleFactor;

            // 方法1：通过反射设置mInputText
            Field[] fields = NumberPicker.class.getDeclaredFields();
            for (Field field : fields) {
                if (field.getName().equals("mInputText")) {
                    field.setAccessible(true);
                    EditText inputText = (EditText) field.get(numberPicker);
                    if (inputText != null) {
                        // 【修复】使用计算后的目标字体大小
                        inputText.setTextSize(android.util.TypedValue.COMPLEX_UNIT_SP, targetTextSize);
                        // 【修复】移除颜色设置，让样式文件中的颜色生效，避免覆盖样式设置
                        // inputText.setTextColor() 调用已移除，颜色由R.style.NumberPickerStyle控制
                    }
                    break;
                }
            }

            // 方法2：遍历所有子视图，设置TextView的字体大小
            // 【修复】使用计算后的目标字体大小
            setTextSizeForAllChildren(numberPicker, targetTextSize);

        } catch (Exception e) {
            LogUtil.w(TAG, "设置NumberPicker文字大小失败", e);
        }
    }

    /**
     * 递归设置所有子视图中TextView的字体大小
     */
    private void setTextSizeForAllChildren(View parent, float textSize) {
        try {
            if (parent instanceof android.view.ViewGroup) {
                android.view.ViewGroup viewGroup = (android.view.ViewGroup) parent;
                for (int i = 0; i < viewGroup.getChildCount(); i++) {
                    View child = viewGroup.getChildAt(i);
                    if (child instanceof TextView) {
                        ((TextView) child).setTextSize(android.util.TypedValue.COMPLEX_UNIT_SP, textSize);
                        // 【修复】移除颜色设置，让样式文件中的颜色生效，避免覆盖样式设置
                        // ((TextView) child).setTextColor() 调用已移除，颜色由R.style.NumberPickerStyle控制
                    } else if (child instanceof android.view.ViewGroup) {
                        setTextSizeForAllChildren(child, textSize);
                    }
                }
            }
        } catch (Exception e) {
            LogUtil.w(TAG, "设置子视图字体大小失败", e);
        }
    }

    /**
     * 为NumberPicker添加字体大小监听器，确保滚动时字体保持一致
     */
    private void addTextSizeListener(NumberPicker numberPicker, float scaleFactor) {
        try {
            // 保存原有的监听器
            NumberPicker.OnValueChangeListener originalListener = null;

            // 设置新的监听器，在值变化时重新设置字体大小
            numberPicker.setOnValueChangedListener((picker, oldVal, newVal) -> {
                // 延迟执行字体设置，确保NumberPicker完成内部更新
                picker.postDelayed(() -> {
                    setNumberPickerTextSize(picker, scaleFactor);
                }, 50);

                // 如果有原有监听器，也要调用
                if (originalListener != null) {
                    originalListener.onValueChange(picker, oldVal, newVal);
                }
            });

        } catch (Exception e) {
            LogUtil.w(TAG, "添加NumberPicker字体监听器失败", e);
        }
    }

    /**
     * 设置对话框圆角背景（与视频播放器一致的8dp圆角）
     */
    private void setDialogRoundedBackground(AlertDialog dialog) {
        try {
            Window window = dialog.getWindow();
            if (window != null) {
                // 创建圆角背景
                GradientDrawable background = new GradientDrawable();
                background.setShape(GradientDrawable.RECTANGLE);
                background.setCornerRadius(24f); // 8dp * 3 = 24px (假设密度为3)
                // 使用对话框专用背景颜色，支持日夜模式切换
                int backgroundColor = ContextCompat.getColor(context, R.color.dialog_background_color);
                background.setColor(backgroundColor);

                // 设置背景
                window.setBackgroundDrawable(background);
                LogUtil.d(TAG, "对话框圆角背景已设置");
            }
        } catch (Exception e) {
            LogUtil.w(TAG, "设置对话框圆角背景失败", e);
        }
    }
    
    /**
     * 更新日期范围（根据月份）
     */
    private void updateDayRange() {
        int daysInMonth = getDaysInMonth(selectedMonth);

        // 如果当前选中的日期超出了新月份的范围，调整到最大值
        if (selectedDay > daysInMonth) {
            selectedDay = daysInMonth;
        }
    }

    /**
     * 更新按钮文本显示
     */
    private void updateButtonText() {
        if (btnMonthPicker != null) {
            btnMonthPicker.setText(selectedMonth + "月");
        }

        if (btnDayPicker != null) {
            if (selectedDay == -1) {
                btnDayPicker.setText("全部日");
            } else {
                btnDayPicker.setText(selectedDay + "日");
            }
        }

        if (btnHourPicker != null) {
            if (selectedHour == -1) {
                btnHourPicker.setText("全部时");
            } else {
                btnHourPicker.setText(String.format("%02d时", selectedHour));
            }
        }
    }
    
    /**
     * 获取指定月份的天数
     */
    private int getDaysInMonth(int month) {
        if (month < 1 || month > 12) {
            return 31;
        }
        
        int days = DAYS_IN_MONTH[month - 1];
        
        // 处理闰年的2月
        if (month == 2) {
            int currentYear = Calendar.getInstance().get(Calendar.YEAR);
            if (isLeapYear(currentYear)) {
                days = 29;
            }
        }
        
        return days;
    }
    
    /**
     * 判断是否为闰年
     */
    private boolean isLeapYear(int year) {
        return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
    }
    
    /**
     * 设置为当前时间（日期和小时默认为"全部"）
     */
    private void setCurrentTime() {
        Calendar calendar = Calendar.getInstance();
        int currentMonth = calendar.get(Calendar.MONTH) + 1; // Calendar.MONTH从0开始

        // 初始化时，日期和小时设置为"全部"（-1）
        setTime(currentMonth, -1, -1);
    }
    
    /**
     * 设置指定时间
     * @param month 月份 (1-12)
     * @param day 日期 (1-31, -1表示全部)
     * @param hour 小时 (0-23, -1表示全部)
     */
    public void setTime(int month, int day, int hour) {
        selectedMonth = month;
        selectedDay = day;
        selectedHour = hour;

        updateDayRange();
        updateButtonText();

        String dayStr = day == -1 ? "全部" : day + "";
        String hourStr = hour == -1 ? "全部" : hour + "";
        LogUtil.d(TAG, "设置时间: " + month + "月" + dayStr + "日" + hourStr + "时");
    }
    
    /**
     * 获取当前选中的时间
     */
    public int getSelectedMonth() {
        return selectedMonth;
    }
    
    public int getSelectedDay() {
        return selectedDay;
    }
    
    public int getSelectedHour() {
        return selectedHour;
    }
    
    /**
     * 获取格式化的时间字符串
     */
    public String getFormattedTime() {
        return String.format("%d月%d日%d时", selectedMonth, selectedDay, selectedHour);
    }
    
    /**
     * 通知时间选择监听器
     */
    private void notifyTimeSelected() {
        if (timeSelectedListener != null) {
            timeSelectedListener.onTimeSelected(selectedMonth, selectedDay, selectedHour);
        }
    }
    
    /**
     * 重置到当前时间
     */
    public void resetToCurrentTime() {
        setCurrentTime();
        notifyTimeSelected();
    }

    /**
     * 重置时间筛选（不触发回调）
     * 用于"全部"按钮点击后重置按钮显示状态
     */
    public void resetTimeFilter() {
        setCurrentTime(); // 重置到当前月份，日期和小时为"全部"
        updateButtonText(); // 更新按钮文本
        LogUtil.d(TAG, "时间筛选已重置为全部");
    }
}
