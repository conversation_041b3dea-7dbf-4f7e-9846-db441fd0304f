# 车辆状态录制控制使用示例

## 快速开始

### 1. 基本使用

```java
// 在Activity或Service中初始化
VehicleRecordingController controller = DIContainer.provideVehicleRecordingController(context);
controller.initialize();

// 手动启动录制（带条件检查）
controller.startManualRecording(new VehicleRecordingController.ConditionCallback() {
    @Override
    public void onConditionMet() {
        Toast.makeText(context, "开始录制", Toast.LENGTH_SHORT).show();
    }
    
    @Override
    public void onConditionNotMet(String reason) {
        Toast.makeText(context, reason, Toast.LENGTH_SHORT).show();
    }
});

// 手动停止录制
controller.stopManualRecording();

// 开启哨兵自动模式
controller.setSentryAutoMode(true);
```

### 2. 在CameraService中使用

```java
public class CameraService extends Service {
    private VehicleRecordingController vehicleController;
    
    @Override
    public void onCreate() {
        super.onCreate();
        
        // 初始化车辆录制控制器
        vehicleController = DIContainer.provideVehicleRecordingController(this);
        vehicleController.initialize();
    }
    
    public void startRecordingWithCheck() {
        vehicleController.startManualRecording(new VehicleRecordingController.ConditionCallback() {
            @Override
            public void onConditionMet() {
                updateNotification("录制已启动");
            }
            
            @Override
            public void onConditionNotMet(String reason) {
                updateNotification("录制失败: " + reason);
            }
        });
    }
    
    @Override
    public void onDestroy() {
        if (vehicleController != null) {
            vehicleController.cleanup();
        }
        super.onDestroy();
    }
}
```

### 3. 在MainActivity中集成UI控制

```java
public class MainActivity extends AppCompatActivity {
    private VehicleRecordingController vehicleController;
    private Switch switchSentryAuto;
    private Button btnRecord;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        
        // 初始化控件
        switchSentryAuto = findViewById(R.id.switch_sentry_auto);
        btnRecord = findViewById(R.id.btn_record);
        
        // 初始化车辆控制器
        vehicleController = DIContainer.provideVehicleRecordingController(this);
        vehicleController.initialize();
        
        // 设置监听器
        setupListeners();
    }
    
    private void setupListeners() {
        // 录制按钮
        btnRecord.setOnClickListener(v -> {
            if (vehicleController.isRecording()) {
                vehicleController.stopManualRecording();
            } else {
                startRecordingWithCheck();
            }
        });
        
        // 哨兵模式开关
        switchSentryAuto.setOnCheckedChangeListener((buttonView, isChecked) -> {
            vehicleController.setSentryAutoMode(isChecked);
            String message = isChecked ? "哨兵模式已开启" : "哨兵模式已关闭";
            Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
        });
    }
    
    private void startRecordingWithCheck() {
        vehicleController.startManualRecording(new VehicleRecordingController.ConditionCallback() {
            @Override
            public void onConditionMet() {
                runOnUiThread(() -> {
                    btnRecord.setText("停止录制");
                    Toast.makeText(MainActivity.this, "开始录制", Toast.LENGTH_SHORT).show();
                });
            }
            
            @Override
            public void onConditionNotMet(String reason) {
                runOnUiThread(() -> {
                    Toast.makeText(MainActivity.this, reason, Toast.LENGTH_LONG).show();
                });
            }
        });
    }
}
```

## 高级用法

### 1. 自定义录制条件

```java
public class CustomVehicleRecordingController extends VehicleRecordingController {
    
    public CustomVehicleRecordingController(Context context, MainViewModel mainViewModel) {
        super(context, mainViewModel);
    }
    
    @Override
    protected boolean isRecordingConditionMet() {
        // 自定义条件：钥匙ON状态 + 非P档 + 车速大于0
        boolean keyOk = currentKeyState >= VehicleManager.KEY_STATE_ON;
        boolean gearOk = currentGearPosition != VehicleManager.GEAR_POSITION_P;
        boolean speedOk = getCurrentSpeed() > 0;
        
        return keyOk && gearOk && speedOk;
    }
    
    @Override
    protected String getConditionFailureReason() {
        if (currentKeyState < VehicleManager.KEY_STATE_ON) {
            return "请启动车辆";
        }
        if (currentGearPosition == VehicleManager.GEAR_POSITION_P) {
            return "请挂入行驶档位";
        }
        if (getCurrentSpeed() <= 0) {
            return "请开始行驶";
        }
        return "车辆状态不满足录制条件";
    }
    
    private float getCurrentSpeed() {
        // 获取当前车速
        return vehicleManager.getFloatProperty(VehiclePropertyKey.KeyVehiclePropertySpeed);
    }
}
```

### 2. 状态监听

```java
public class VehicleStateMonitor implements VehicleManager.VehicleDataListener {
    
    @Override
    public void onVehiclePropertyChanged(VehiclePropertyKey key, Object value, boolean selfGet) {
        if (key == VehiclePropertyKey.KeyVehiclePropertyKeySts) {
            int keyState = (Integer) value;
            Log.i("VehicleState", "钥匙状态变化: " + getKeyStateName(keyState));
            
            // 处理钥匙状态变化
            handleKeyStateChange(keyState);
            
        } else if (key == VehiclePropertyKey.KeyVehiclePropertyGearPosition) {
            int gearPosition = (Integer) value;
            Log.i("VehicleState", "档位变化: " + getGearPositionName(gearPosition));
            
            // 处理档位变化
            handleGearPositionChange(gearPosition);
        }
    }
    
    private void handleKeyStateChange(int keyState) {
        switch (keyState) {
            case VehicleManager.KEY_STATE_OFF:
                // 钥匙关闭，停止所有录制
                stopAllRecording();
                break;
            case VehicleManager.KEY_STATE_ACC:
                // 钥匙ACC，准备录制
                prepareRecording();
                break;
            case VehicleManager.KEY_STATE_ON:
                // 钥匙ON，可以开始录制
                enableRecording();
                break;
        }
    }
    
    private void handleGearPositionChange(int gearPosition) {
        if (gearPosition == VehicleManager.GEAR_POSITION_P) {
            // 挂入P档，停止录制
            stopRecordingForParking();
        } else {
            // 挂入行驶档位，允许录制
            enableRecordingForDriving();
        }
    }
}
```

### 3. 错误处理和降级

```java
public class RobustVehicleController {
    private VehicleRecordingController vehicleController;
    private MainViewModel mainViewModel;
    private boolean fallbackMode = false;
    
    public void initialize(Context context) {
        try {
            vehicleController = DIContainer.provideVehicleRecordingController(context);
            vehicleController.initialize();
            fallbackMode = false;
            Log.i("VehicleController", "车辆控制器初始化成功");
        } catch (Exception e) {
            Log.e("VehicleController", "车辆控制器初始化失败，启用降级模式", e);
            fallbackMode = true;
            mainViewModel = DIContainer.provideMainViewModel(context);
        }
    }
    
    public void startRecording(ConditionCallback callback) {
        if (!fallbackMode && vehicleController != null) {
            // 使用车辆状态检查
            vehicleController.startManualRecording(callback);
        } else {
            // 降级到普通录制
            Log.w("VehicleController", "使用降级模式启动录制");
            mainViewModel.startAllCamerasRecording();
            if (callback != null) {
                callback.onConditionMet();
            }
        }
    }
    
    public void stopRecording() {
        if (!fallbackMode && vehicleController != null) {
            vehicleController.stopManualRecording();
        } else {
            mainViewModel.stopAllCamerasRecording();
        }
    }
    
    public boolean isVehicleControlAvailable() {
        return !fallbackMode && vehicleController != null;
    }
}
```

## 测试示例

### 1. 单元测试

```java
@Test
public void testRecordingConditions() {
    VehicleRecordingController controller = new VehicleRecordingController(context, mockViewModel);
    
    // 测试各种车辆状态组合
    testCondition(KEY_STATE_OFF, GEAR_POSITION_P, false, "钥匙关闭+P档");
    testCondition(KEY_STATE_ACC, GEAR_POSITION_P, false, "钥匙ACC+P档");
    testCondition(KEY_STATE_ACC, GEAR_POSITION_D, true, "钥匙ACC+D档");
    testCondition(KEY_STATE_ON, GEAR_POSITION_D, true, "钥匙ON+D档");
}

private void testCondition(int keyState, int gearPosition, boolean shouldAllow, String description) {
    // 模拟车辆状态
    controller.onVehiclePropertyChanged(VehiclePropertyKey.KeyVehiclePropertyKeySts, keyState, false);
    controller.onVehiclePropertyChanged(VehiclePropertyKey.KeyVehiclePropertyGearPosition, gearPosition, false);
    
    // 测试录制启动
    controller.startManualRecording(new ConditionCallback() {
        @Override
        public void onConditionMet() {
            assertTrue(description + " 应该允许录制", shouldAllow);
        }
        
        @Override
        public void onConditionNotMet(String reason) {
            assertFalse(description + " 不应该允许录制", shouldAllow);
        }
    });
}
```

### 2. 集成测试

```java
@Test
public void testFullWorkflow() {
    // 1. 初始化
    VehicleRecordingController controller = DIContainer.provideVehicleRecordingController(context);
    controller.initialize();
    
    // 2. 模拟车辆启动
    simulateVehicleStartup(controller);
    
    // 3. 开启哨兵模式
    controller.setSentryAutoMode(true);
    assertTrue("哨兵模式应该开启", controller.isSentryAutoModeEnabled());
    
    // 4. 模拟挂档行驶
    simulateGearChange(controller, VehicleManager.GEAR_POSITION_D);
    assertTrue("应该自动开始录制", controller.isRecording());
    
    // 5. 模拟停车
    simulateGearChange(controller, VehicleManager.GEAR_POSITION_P);
    assertFalse("应该自动停止录制", controller.isRecording());
    
    // 6. 清理
    controller.cleanup();
}
```

## 注意事项

1. **线程安全**: 车辆状态回调可能在后台线程，UI更新需要切换到主线程
2. **内存管理**: 及时调用cleanup()方法释放资源
3. **异常处理**: 车辆服务可能不可用，需要提供降级方案
4. **性能优化**: 避免频繁的状态检查，使用防抖机制
5. **用户体验**: 提供清晰的状态提示和错误信息
