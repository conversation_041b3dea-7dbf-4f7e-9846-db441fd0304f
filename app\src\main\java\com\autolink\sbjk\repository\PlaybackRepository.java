package com.autolink.sbjk.repository;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.autolink.sbjk.model.VideoRecordInfo;
import com.autolink.sbjk.scanner.QuickVideoScanner;
import com.autolink.sbjk.common.util.LogUtil;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 回放数据仓库
 * MVVM架构的数据层，管理录像数据的获取、缓存和筛选
 */
public class PlaybackRepository {
    
    private static final String TAG = "PlaybackRepository";
    
    // 单例实例
    private static volatile PlaybackRepository instance;
    
    // 文件扫描器
    private QuickVideoScanner scanner;
    
    // 数据缓存
    private final MutableLiveData<List<VideoRecordInfo>> allVideosLiveData;
    private final MutableLiveData<List<VideoRecordInfo>> filteredVideosLiveData;
    private final MutableLiveData<Boolean> loadingLiveData;
    private final MutableLiveData<String> errorLiveData;
    
    // 缓存数据
    private List<VideoRecordInfo> cachedAllVideos;
    private long lastScanTime;
    
    // 当前筛选条件
    private String currentCameraFilter = "全部";
    private int currentMonth = -1;  // -1表示不筛选月份
    private int currentDay = -1;    // -1表示不筛选日期
    private int currentHour = -1;   // -1表示不筛选小时
    
    private PlaybackRepository() {
        this.scanner = new QuickVideoScanner();
        this.allVideosLiveData = new MutableLiveData<>();
        this.filteredVideosLiveData = new MutableLiveData<>();
        this.loadingLiveData = new MutableLiveData<>();
        this.errorLiveData = new MutableLiveData<>();
        this.cachedAllVideos = new ArrayList<>();
        this.lastScanTime = 0;
    }
    
    /**
     * 获取单例实例
     */
    public static PlaybackRepository getInstance() {
        if (instance == null) {
            synchronized (PlaybackRepository.class) {
                if (instance == null) {
                    instance = new PlaybackRepository();
                }
            }
        }
        return instance;
    }
    
    /**
     * 获取所有录像数据的LiveData
     */
    public LiveData<List<VideoRecordInfo>> getAllVideos() {
        return allVideosLiveData;
    }
    
    /**
     * 获取筛选后录像数据的LiveData
     */
    public LiveData<List<VideoRecordInfo>> getFilteredVideos() {
        return filteredVideosLiveData;
    }
    
    /**
     * 获取加载状态的LiveData
     */
    public LiveData<Boolean> getLoadingState() {
        return loadingLiveData;
    }
    
    /**
     * 获取错误信息的LiveData
     */
    public LiveData<String> getErrorMessage() {
        return errorLiveData;
    }
    
    /**
     * 加载所有录像文件
     */
    public void loadAllVideos() {
        loadingLiveData.setValue(true);

        // 确保scanner可用
        if (scanner == null) {
            scanner = new QuickVideoScanner();
        }

        scanner.quickScanAsync()
            .thenAccept(videos -> {
                // 更新缓存
                cachedAllVideos = videos;
                lastScanTime = System.currentTimeMillis();
                
                // 更新LiveData
                allVideosLiveData.postValue(videos);
                
                // 应用当前筛选条件
                applyCurrentFilters();
                
                loadingLiveData.postValue(false);
                
                LogUtil.i(TAG, "录像加载完成，共 " + videos.size() + " 个文件");
            })
            .exceptionally(throwable -> {
                LogUtil.e(TAG, "加载录像失败", throwable);
                errorLiveData.postValue("加载录像失败: " + throwable.getMessage());
                loadingLiveData.postValue(false);
                return null;
            });
    }
    
    /**
     * 刷新录像列表（检查新文件）
     */
    public void refreshVideos() {
        if (lastScanTime == 0) {
            // 如果从未扫描过，执行完整扫描
            loadAllVideos();
            return;
        }
        
        // 检查新文件
        CompletableFuture.supplyAsync(() -> scanner.checkNewCompletedFiles(lastScanTime))
            .thenAccept(newVideos -> {
                if (!newVideos.isEmpty()) {
                    // 合并新文件到缓存
                    List<VideoRecordInfo> updatedVideos = new ArrayList<>(cachedAllVideos);
                    updatedVideos.addAll(newVideos);
                    
                    // 重新排序
                    updatedVideos.sort((a, b) -> Long.compare(b.getTimestamp(), a.getTimestamp()));
                    
                    // 更新缓存和LiveData
                    cachedAllVideos = updatedVideos;
                    allVideosLiveData.postValue(updatedVideos);
                    
                    // 应用筛选
                    applyCurrentFilters();
                    
                    LogUtil.i(TAG, "发现 " + newVideos.size() + " 个新录像文件");
                }
                
                lastScanTime = System.currentTimeMillis();
            })
            .exceptionally(throwable -> {
                LogUtil.e(TAG, "刷新录像失败", throwable);
                return null;
            });
    }
    
    /**
     * 按摄像头筛选
     */
    public void filterByCamera(String cameraDirection) {
        currentCameraFilter = cameraDirection;
        applyCurrentFilters();
    }
    
    /**
     * 按精确时间筛选
     */
    public void filterByTime(int month, int day, int hour) {
        currentMonth = month;
        currentDay = day;
        currentHour = hour;
        applyCurrentFilters();
    }

    /**
     * 重置时间筛选
     */
    public void resetTimeFilter() {
        currentMonth = -1;
        currentDay = -1;
        currentHour = -1;
        applyCurrentFilters();
    }
    
    /**
     * 应用当前的筛选条件
     */
    private void applyCurrentFilters() {
        List<VideoRecordInfo> filtered = new ArrayList<>(cachedAllVideos);
        
        // 应用摄像头筛选
        if (!"全部".equals(currentCameraFilter)) {
            filtered = scanner.filterByCamera(filtered, currentCameraFilter);
        }
        
        // 应用时间筛选
        if (currentMonth != -1 || currentDay != -1 || currentHour != -1) {
            filtered = filterByExactTime(filtered, currentMonth, currentDay, currentHour);
        }
        
        filteredVideosLiveData.postValue(filtered);
        
        String timeDesc = getTimeFilterDescription();
        LogUtil.d(TAG, "筛选完成: " + filtered.size() + " 个文件 (摄像头:" +
                 currentCameraFilter + ", 时间:" + timeDesc + ")");
    }
    
    /**
     * 按精确时间筛选
     */
    private List<VideoRecordInfo> filterByExactTime(List<VideoRecordInfo> videos, int month, int day, int hour) {
        List<VideoRecordInfo> filtered = new ArrayList<>();

        Calendar calendar = Calendar.getInstance();

        for (VideoRecordInfo video : videos) {
            calendar.setTimeInMillis(video.getTimestamp());

            int videoMonth = calendar.get(Calendar.MONTH) + 1; // Calendar.MONTH从0开始
            int videoDay = calendar.get(Calendar.DAY_OF_MONTH);
            int videoHour = calendar.get(Calendar.HOUR_OF_DAY);

            boolean match = true;

            // 检查月份匹配
            if (month != -1 && videoMonth != month) {
                match = false;
            }

            // 检查日期匹配
            if (day != -1 && videoDay != day) {
                match = false;
            }

            // 检查小时匹配
            if (hour != -1 && videoHour != hour) {
                match = false;
            }

            if (match) {
                filtered.add(video);
            }
        }

        return filtered;
    }

    /**
     * 按时间范围筛选（保留原方法以备用）
     */
    private List<VideoRecordInfo> filterByTimeRange(List<VideoRecordInfo> videos, String timeFilter) {
        long currentTime = System.currentTimeMillis();
        long startTime = 0;
        long endTime = currentTime;
        
        Calendar calendar = Calendar.getInstance();
        
        switch (timeFilter) {
            case "月":
                // 当前月份
                calendar.setTimeInMillis(currentTime);
                calendar.set(Calendar.DAY_OF_MONTH, 1);
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                startTime = calendar.getTimeInMillis();
                break;
                
            case "日":
                // 今天
                calendar.setTimeInMillis(currentTime);
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                startTime = calendar.getTimeInMillis();
                break;
                
            case "时":
                // 最近一小时
                startTime = currentTime - (60 * 60 * 1000);
                break;
                
            default:
                // 全部时间
                return videos;
        }
        
        return scanner.filterByTimeRange(videos, startTime, endTime);
    }
    
    /**
     * 获取当前筛选条件
     */
    public String getCurrentCameraFilter() {
        return currentCameraFilter;
    }
    
    public String getTimeFilterDescription() {
        if (currentMonth == -1 && currentDay == -1 && currentHour == -1) {
            return "全部";
        }

        StringBuilder desc = new StringBuilder();
        if (currentMonth != -1) {
            desc.append(currentMonth).append("月");
        }
        if (currentDay != -1) {
            desc.append(currentDay).append("日");
        }
        if (currentHour != -1) {
            desc.append(currentHour).append("时");
        }

        return desc.toString();
    }

    public int getCurrentMonth() {
        return currentMonth;
    }

    public int getCurrentDay() {
        return currentDay;
    }

    public int getCurrentHour() {
        return currentHour;
    }
    
    /**
     * 清除错误信息
     */
    public void clearError() {
        errorLiveData.setValue(null);
    }

    /**
     * 清理资源（生命周期结束时调用）
     */
    public void cleanup() {
        try {
            // 停止扫描器
            if (scanner != null) {
                scanner.shutdown();
                scanner = null; // 清空引用，下次使用时会重新创建
            }

            // 清理缓存数据
            cachedAllVideos.clear();

            // 重置LiveData为初始状态
            allVideosLiveData.setValue(new ArrayList<>());
            filteredVideosLiveData.setValue(new ArrayList<>());
            loadingLiveData.setValue(false);
            errorLiveData.setValue(null);

            // 重置筛选条件
            currentCameraFilter = "全部";
            currentMonth = -1;
            currentDay = -1;
            currentHour = -1;

            LogUtil.d(TAG, "PlaybackRepository资源清理完成");

        } catch (Exception e) {
            LogUtil.e(TAG, "清理PlaybackRepository资源时出错", e);
        }
    }
    
    /**
     * 添加新录像文件（用于实时更新）
     */
    public void addNewVideo(VideoRecordInfo newVideo) {
        if (newVideo != null && newVideo.isValid()) {
            List<VideoRecordInfo> updatedVideos = new ArrayList<>(cachedAllVideos);
            
            // 检查是否已存在
            boolean exists = false;
            for (VideoRecordInfo video : updatedVideos) {
                if (video.getFilePath().equals(newVideo.getFilePath())) {
                    exists = true;
                    break;
                }
            }
            
            if (!exists) {
                updatedVideos.add(0, newVideo); // 添加到列表开头
                
                // 重新排序
                updatedVideos.sort((a, b) -> Long.compare(b.getTimestamp(), a.getTimestamp()));
                
                // 更新缓存和LiveData
                cachedAllVideos = updatedVideos;
                allVideosLiveData.setValue(updatedVideos);
                
                // 应用筛选
                applyCurrentFilters();
                
                LogUtil.i(TAG, "添加新录像: " + newVideo.getCameraDirection() + " " + newVideo.getDisplayTime());
            }
        }
    }
}
