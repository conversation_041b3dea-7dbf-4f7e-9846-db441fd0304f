# UI交互设计 - 哨兵模式与手动录制

## 设计理念

**避免用户操作冲突，确保界面逻辑清晰**

当开启自动启动哨兵功能时，隐藏手动录制按钮，避免用户在自动模式下进行手动操作，防止状态混乱。

## 交互逻辑

### 1. 哨兵模式关闭状态（默认）

```
UI状态：
├── 哨兵自动开关: OFF
├── 手动录制按钮: 显示 (VISIBLE)
└── 录制控制: 完全手动

用户操作：
├── 可以手动点击录制按钮
├── 可以手动长按停止录制
└── 可以开启哨兵自动模式
```

### 2. 哨兵模式开启状态

```
UI状态：
├── 哨兵自动开关: ON
├── 手动录制按钮: 隐藏 (GONE)
└── 录制控制: 完全自动

系统行为：
├── 监听车辆状态变化
├── 条件满足时自动启动录制
├── 条件不满足时自动停止录制
└── 用户只能通过关闭哨兵模式来停止自动功能
```

## 实现细节

### 1. UI控制逻辑

```java
// MainActivity.java
private void updateRecordingButtonVisibility(boolean sentryAutoEnabled) {
    if (btnAllCameras != null) {
        if (sentryAutoEnabled) {
            // 开启哨兵自动模式时，隐藏手动录制按钮
            btnAllCameras.setVisibility(View.GONE);
            LogUtil.i(TAG, "Recording button hidden - sentry auto mode enabled");
        } else {
            // 关闭哨兵自动模式时，显示手动录制按钮
            btnAllCameras.setVisibility(View.VISIBLE);
            LogUtil.i(TAG, "Recording button visible - sentry auto mode disabled");
        }
    }
}
```

### 2. 状态切换流程

```java
public void setSentryAutoMode(boolean enabled) {
    sentryModeEnabled = enabled;
    
    // 1. 首先控制UI显示
    updateRecordingButtonVisibility(enabled);
    
    // 2. 然后设置车辆控制逻辑
    if (cameraService != null) {
        cameraService.setSentryAutoMode(enabled);
    }
    
    // 3. 最后给用户反馈
    String message = enabled ? "哨兵模式已开启，满足条件时自动录制" : "哨兵模式已关闭";
    showToast(message);
}
```

### 3. 防护逻辑

```java
// VehicleRecordingController.java
public void stopManualRecording() {
    if (isCurrentlyRecording && currentStartMode == RecordingStartMode.MANUAL) {
        // 只允许停止手动启动的录制
        currentStartMode = RecordingStartMode.NONE;
        isCurrentlyRecording = false;
        mainViewModel.stopAllCamerasRecording();
    } else if (currentStartMode == RecordingStartMode.AUTO_SENTRY) {
        // 防止意外调用
        LogUtil.w(TAG, "Cannot manually stop auto-started recording, please disable sentry mode first");
    }
}
```

## 用户体验流程

### 场景1：用户想要手动录制

```
1. 确保哨兵自动开关处于关闭状态
2. 点击录制按钮
3. 系统检查车辆状态
4. 满足条件 → 开始录制
5. 不满足条件 → 显示提示"请挂入P档后启动"
6. 长按录制按钮停止录制
```

### 场景2：用户想要自动录制

```
1. 开启哨兵自动开关
2. 手动录制按钮自动隐藏
3. 系统立即检查车辆状态
4. 满足条件 → 自动开始录制 + 提示"哨兵模式自动启动录制"
5. 不满足条件 → 提示"请挂入P档后启动"
6. 系统持续监听车辆状态变化
7. 条件变化时自动启动/停止录制
```

### 场景3：从自动模式切换到手动模式

```
1. 关闭哨兵自动开关
2. 如果正在自动录制 → 立即停止录制 + 提示"哨兵模式已关闭"
3. 手动录制按钮重新显示
4. 用户可以进行手动录制操作
```

## 设计优势

### 1. 避免操作冲突 ✅
- 自动模式时隐藏手动按钮
- 防止用户在自动录制时进行手动操作
- 避免状态混乱和用户困惑

### 2. 界面逻辑清晰 ✅
- 当前模式一目了然
- 可用操作明确显示
- 不可用操作直接隐藏

### 3. 用户体验友好 ✅
- 操作简单直观
- 状态反馈及时
- 错误操作被预防

### 4. 代码逻辑简化 ✅
- 减少状态判断复杂度
- 避免手动停止自动录制的处理
- 降低维护成本

## 状态图

```
┌─────────────────┐    开启哨兵模式    ┌─────────────────┐
│   手动模式      │ ──────────────→ │   自动模式      │
│                 │                  │                 │
│ 哨兵开关: OFF   │                  │ 哨兵开关: ON    │
│ 录制按钮: 显示  │                  │ 录制按钮: 隐藏  │
│ 控制方式: 手动  │                  │ 控制方式: 自动  │
└─────────────────┘ ←────────────── └─────────────────┘
                     关闭哨兵模式
```

## 技术实现要点

### 1. 初始化时的状态同步
```java
// 在setupButtonListeners()最后
updateRecordingButtonVisibility(sentryModeEnabled);
```

### 2. 状态持久化（可选）
```java
// 可以将哨兵模式状态保存到SharedPreferences
// 应用重启时恢复上次的状态
```

### 3. 异常处理
```java
// 确保在各种异常情况下UI状态正确
// 如服务连接失败时的降级处理
```

## 总结

这种设计方案通过**UI状态控制**来避免用户操作冲突，确保：

1. **模式互斥** - 手动模式和自动模式不会同时存在
2. **操作明确** - 用户只能看到当前可用的操作
3. **状态一致** - UI显示与实际功能状态完全一致
4. **体验流畅** - 模式切换简单直观

这是一个更加合理和用户友好的交互设计方案！
