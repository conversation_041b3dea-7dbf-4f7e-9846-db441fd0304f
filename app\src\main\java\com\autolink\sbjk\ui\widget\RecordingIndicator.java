package com.autolink.sbjk.ui.widget;

import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.view.View;
import android.view.animation.LinearInterpolator;

import androidx.annotation.Nullable;

import com.autolink.sbjk.common.util.LogUtil;

/**
 * 录制状态指示器
 * 显示录制状态的自定义View组件
 */
public class RecordingIndicator extends View {
    
    private static final String TAG = "RecordingIndicator";
    
    // 状态常量
    public enum RecordingState {
        IDLE,           // 空闲状态
        RECORDING,      // 录制中
        PAUSED,         // 暂停
        ERROR           // 错误状态
    }
    
    // 绘制相关
    private Paint backgroundPaint;
    private Paint indicatorPaint;
    private Paint textPaint;
    private RectF indicatorRect;
    
    // 状态相关
    private RecordingState currentState = RecordingState.IDLE;
    private String statusText = "";
    private long recordingDuration = 0;
    
    // 动画相关
    private ValueAnimator pulseAnimator;
    private float pulseAlpha = 1.0f;
    
    // 尺寸相关
    private float indicatorRadius;
    private float textSize;
    private int viewWidth;
    private int viewHeight;
    
    // 颜色配置
    private static final int COLOR_IDLE = Color.GRAY;
    private static final int COLOR_RECORDING = Color.RED;
    private static final int COLOR_PAUSED = Color.YELLOW;
    private static final int COLOR_ERROR = Color.parseColor("#FF6B6B");
    private static final int COLOR_BACKGROUND = Color.parseColor("#33000000");
    private static final int COLOR_TEXT = Color.WHITE;
    
    public RecordingIndicator(Context context) {
        super(context);
        init();
    }
    
    public RecordingIndicator(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }
    
    public RecordingIndicator(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }
    
    private void init() {
        // 初始化画笔
        backgroundPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        backgroundPaint.setColor(COLOR_BACKGROUND);
        backgroundPaint.setStyle(Paint.Style.FILL);
        
        indicatorPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        indicatorPaint.setColor(COLOR_IDLE);
        indicatorPaint.setStyle(Paint.Style.FILL);
        
        textPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        textPaint.setColor(COLOR_TEXT);
        textPaint.setTextAlign(Paint.Align.CENTER);
        
        indicatorRect = new RectF();
        
        // 初始化脉冲动画
        initPulseAnimation();
        
        LogUtil.d(TAG, "RecordingIndicator initialized");
    }
    
    private void initPulseAnimation() {
        pulseAnimator = ValueAnimator.ofFloat(0.3f, 1.0f);
        pulseAnimator.setDuration(1000);
        pulseAnimator.setRepeatCount(ValueAnimator.INFINITE);
        pulseAnimator.setRepeatMode(ValueAnimator.REVERSE);
        pulseAnimator.setInterpolator(new LinearInterpolator());
        
        pulseAnimator.addUpdateListener(animation -> {
            pulseAlpha = (float) animation.getAnimatedValue();
            invalidate();
        });
    }
    
    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        
        viewWidth = w;
        viewHeight = h;
        
        // 计算指示器尺寸
        indicatorRadius = Math.min(w, h) * 0.15f;
        textSize = Math.min(w, h) * 0.08f;
        textPaint.setTextSize(textSize);
        
        // 更新指示器矩形
        float centerX = w / 2f;
        float centerY = h / 2f;
        indicatorRect.set(
            centerX - indicatorRadius,
            centerY - indicatorRadius,
            centerX + indicatorRadius,
            centerY + indicatorRadius
        );
        
        LogUtil.d(TAG, "Size changed: " + w + "x" + h + ", indicatorRadius: " + indicatorRadius);
    }
    
    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        
        if (viewWidth == 0 || viewHeight == 0) {
            return;
        }
        
        // 绘制背景
        canvas.drawRoundRect(0, 0, viewWidth, viewHeight, 
                           viewHeight * 0.1f, viewHeight * 0.1f, backgroundPaint);
        
        // 绘制录制指示器
        drawRecordingIndicator(canvas);
        
        // 绘制状态文本
        drawStatusText(canvas);
    }
    
    private void drawRecordingIndicator(Canvas canvas) {
        // 设置指示器颜色和透明度
        int color = getIndicatorColor();
        if (currentState == RecordingState.RECORDING) {
            // 录制时使用脉冲效果
            int alpha = (int) (255 * pulseAlpha);
            indicatorPaint.setColor(Color.argb(alpha, Color.red(color), Color.green(color), Color.blue(color)));
        } else {
            indicatorPaint.setColor(color);
        }
        
        // 绘制圆形指示器
        canvas.drawCircle(indicatorRect.centerX(), indicatorRect.centerY(), 
                         indicatorRadius, indicatorPaint);
        
        // 录制状态下绘制内部小圆点
        if (currentState == RecordingState.RECORDING) {
            indicatorPaint.setColor(Color.WHITE);
            canvas.drawCircle(indicatorRect.centerX(), indicatorRect.centerY(), 
                             indicatorRadius * 0.3f, indicatorPaint);
        }
    }
    
    private void drawStatusText(Canvas canvas) {
        float centerX = viewWidth / 2f;
        float textY = indicatorRect.bottom + textSize * 1.5f;
        
        // 绘制状态文本
        if (!statusText.isEmpty()) {
            canvas.drawText(statusText, centerX, textY, textPaint);
        }
        
        // 绘制录制时长
        if (currentState == RecordingState.RECORDING && recordingDuration > 0) {
            String durationText = formatDuration(recordingDuration);
            canvas.drawText(durationText, centerX, textY + textSize * 1.2f, textPaint);
        }
    }
    
    private int getIndicatorColor() {
        switch (currentState) {
            case RECORDING:
                return COLOR_RECORDING;
            case PAUSED:
                return COLOR_PAUSED;
            case ERROR:
                return COLOR_ERROR;
            case IDLE:
            default:
                return COLOR_IDLE;
        }
    }
    
    private String formatDuration(long durationMs) {
        long seconds = durationMs / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        
        seconds = seconds % 60;
        minutes = minutes % 60;
        
        if (hours > 0) {
            return String.format("%02d:%02d:%02d", hours, minutes, seconds);
        } else {
            return String.format("%02d:%02d", minutes, seconds);
        }
    }
    
    /**
     * 设置录制状态
     */
    public void setRecordingState(RecordingState state) {
        if (this.currentState != state) {
            this.currentState = state;
            
            // 控制脉冲动画
            if (state == RecordingState.RECORDING) {
                if (!pulseAnimator.isRunning()) {
                    pulseAnimator.start();
                }
            } else {
                if (pulseAnimator.isRunning()) {
                    pulseAnimator.cancel();
                }
                pulseAlpha = 1.0f;
            }
            
            invalidate();
            LogUtil.d(TAG, "Recording state changed to: " + state);
        }
    }
    
    /**
     * 设置状态文本
     */
    public void setStatusText(String text) {
        this.statusText = text != null ? text : "";
        invalidate();
    }
    
    /**
     * 设置录制时长
     */
    public void setRecordingDuration(long durationMs) {
        this.recordingDuration = durationMs;
        if (currentState == RecordingState.RECORDING) {
            invalidate();
        }
    }
    
    /**
     * 获取当前录制状态
     */
    public RecordingState getRecordingState() {
        return currentState;
    }
    
    /**
     * 开始录制指示
     */
    public void startRecording() {
        setRecordingState(RecordingState.RECORDING);
        setStatusText("录制中");
    }
    
    /**
     * 停止录制指示
     */
    public void stopRecording() {
        setRecordingState(RecordingState.IDLE);
        setStatusText("就绪");
        setRecordingDuration(0);
    }
    
    /**
     * 暂停录制指示
     */
    public void pauseRecording() {
        setRecordingState(RecordingState.PAUSED);
        setStatusText("已暂停");
    }
    
    /**
     * 显示错误状态
     */
    public void showError(String errorMessage) {
        setRecordingState(RecordingState.ERROR);
        setStatusText(errorMessage != null ? errorMessage : "错误");
    }
    
    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        
        // 停止动画
        if (pulseAnimator != null && pulseAnimator.isRunning()) {
            pulseAnimator.cancel();
        }
        
        LogUtil.d(TAG, "RecordingIndicator detached from window");
    }
}
