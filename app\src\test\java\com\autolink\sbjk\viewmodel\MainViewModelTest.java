package com.autolink.sbjk.viewmodel;

import android.app.Application;
import androidx.arch.core.executor.testing.InstantTaskExecutorRule;
import androidx.lifecycle.Observer;

import com.autolink.sbjk.di.DIContainer;
import com.autolink.sbjk.model.entity.CameraInfo;
import com.autolink.sbjk.model.repository.CameraRepository;
import com.autolink.sbjk.model.repository.RecordingRepository;
import com.autolink.sbjk.model.state.CameraState;

import org.junit.After;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.RuntimeEnvironment;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * MainViewModel单元测试
 * 测试主界面ViewModel的业务逻辑
 */
@RunWith(RobolectricTestRunner.class)
public class MainViewModelTest {
    
    @Rule
    public InstantTaskExecutorRule instantTaskExecutorRule = new InstantTaskExecutorRule();
    
    @Mock
    private CameraRepository mockCameraRepository;
    
    @Mock
    private RecordingRepository mockRecordingRepository;
    
    @Mock
    private Observer<Boolean> mockRecordingObserver;
    
    @Mock
    private Observer<String> mockErrorObserver;
    
    @Mock
    private Observer<CameraState> mockStateObserver;
    
    private MainViewModel viewModel;
    private Application application;
    
    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        application = RuntimeEnvironment.getApplication();

        // 使用依赖注入创建ViewModel实例
        viewModel = DIContainer.provideMainViewModel(mockCameraRepository, mockRecordingRepository);

        // 设置观察者
        viewModel.isAllCamerasRecording.observeForever(mockRecordingObserver);
        viewModel.errorMessage.observeForever(mockErrorObserver);
        viewModel.aggregatedCameraState.observeForever(mockStateObserver);
    }

    @After
    public void tearDown() {
        // 清理DI容器实例
        DIContainer.clearInstances();
    }
    
    @Test
    public void testInitialState() {
        // 验证初始状态
        assertFalse("初始状态应该不在录制", 
                   Boolean.TRUE.equals(viewModel.isAllCamerasRecording.getValue()));
        
        assertFalse("初始状态应该不在加载", 
                   Boolean.TRUE.equals(viewModel.isLoading.getValue()));
        
        assertEquals("初始录制时长应该为00:00:00", 
                    "00:00:00", viewModel.recordingDuration.getValue());
    }
    
    @Test
    public void testStartAllCamerasRecording() {
        // 执行开始录制
        viewModel.startAllCamerasRecording();
        
        // 验证加载状态被设置
        verify(mockRecordingObserver, timeout(1000)).onChanged(any());
        
        // 验证Repository方法被调用
        // 注意：这里需要修改ViewModel以支持依赖注入才能进行mock验证
    }
    
    @Test
    public void testStopAllCamerasRecording() {
        // 先设置为录制状态
        // viewModel.startAllCamerasRecording();
        
        // 执行停止录制
        viewModel.stopAllCamerasRecording();
        
        // 验证状态变化
        verify(mockRecordingObserver, timeout(1000)).onChanged(any());
    }
    
    @Test
    public void testToggleRecording() {
        // 测试切换录制状态
        Boolean initialState = viewModel.isAllCamerasRecording.getValue();
        
        viewModel.toggleRecording();
        
        // 验证状态发生变化
        verify(mockRecordingObserver, timeout(1000)).onChanged(any());
    }
    
    @Test
    public void testErrorHandling() {
        // 模拟错误情况
        // 这里需要触发一个会产生错误的操作
        
        // 验证错误信息被设置
        verify(mockErrorObserver, timeout(1000)).onChanged(anyString());
    }
    
    @Test
    public void testCameraInfoRetrieval() {
        // 测试获取相机信息
        String cameraId = "26";
        CameraInfo cameraInfo = viewModel.getCameraInfo(cameraId);
        
        // 验证返回的相机信息
        assertNotNull("相机信息不应该为空", cameraInfo);
    }
    
    @Test
    public void testSystemHealthCheck() {
        // 测试系统健康检查
        boolean isHealthy = viewModel.isSystemHealthy();
        
        // 根据当前状态验证结果
        // 这里的具体验证逻辑取决于mock的设置
    }
    
    @Test
    public void testRecordingDurationFormat() {
        // 测试录制时长格式化
        // 这里需要访问私有方法或者通过公共接口测试
        
        // 可以通过反射测试私有方法
        // 或者通过设置录制状态并等待时长更新来测试
    }
    
    @Test
    public void testViewModelCleanup() {
        // 测试ViewModel清理
        viewModel.onCleared();
        
        // 验证资源被正确释放
        // 这里需要检查Handler回调是否被移除等
    }
    
    @Test
    public void testConcurrentOperations() {
        // 测试并发操作
        // 同时启动多个相机录制操作
        
        viewModel.startCameraRecording("26");
        viewModel.startCameraRecording("30");
        viewModel.startCameraRecording("34");
        viewModel.startCameraRecording("38");
        
        // 验证所有操作都能正确处理
        verify(mockRecordingObserver, timeout(2000).atLeast(1)).onChanged(any());
    }
    
    @Test
    public void testLoadingStateManagement() {
        // 测试加载状态管理
        assertFalse("初始加载状态应该为false", 
                   Boolean.TRUE.equals(viewModel.isLoading.getValue()));
        
        // 执行一个会触发加载状态的操作
        viewModel.startAllCamerasRecording();
        
        // 验证加载状态变化
        // 注意：由于异步操作，可能需要使用timeout
    }
    
    @Test
    public void testErrorRecovery() {
        // 测试错误恢复
        // 先触发一个错误
        // 然后清除错误
        viewModel.clearAllErrors();

        // 验证错误被清除
        assertNull("错误应该被清除", viewModel.errorMessage.getValue());
    }

    // 新增的预览Surface管理测试
    @Test
    public void testPreviewSurfaceAvailable() {
        // 准备测试数据
        String cameraId = "test_camera";
        android.view.Surface mockSurface = mock(android.view.Surface.class);

        // 执行预览Surface可用
        viewModel.onPreviewSurfaceAvailable(cameraId, mockSurface);

        // 验证Repository方法被调用
        verify(mockCameraRepository).setPreviewSurface(cameraId, mockSurface);
    }

    @Test
    public void testPreviewSurfaceChanged() {
        // 准备测试数据
        String cameraId = "test_camera";
        android.view.Surface mockSurface = mock(android.view.Surface.class);

        // 执行预览Surface变化
        viewModel.onPreviewSurfaceChanged(cameraId, mockSurface);

        // 验证Repository方法被调用
        verify(mockCameraRepository).updatePreviewSurface(cameraId, mockSurface);
    }

    @Test
    public void testPreviewSurfaceDestroyed() {
        // 准备测试数据
        String cameraId = "test_camera";

        // 执行预览Surface销毁
        viewModel.onPreviewSurfaceDestroyed(cameraId);

        // 验证Repository方法被调用
        verify(mockCameraRepository).clearPreviewSurface(cameraId);
    }

    @Test
    public void testDependencyInjectionConstructor() {
        // 测试依赖注入构造函数
        MainViewModel testViewModel = new MainViewModel(mockCameraRepository, mockRecordingRepository);

        assertNotNull(testViewModel);
        assertNotNull(testViewModel.isAllCamerasRecording);
        assertNotNull(testViewModel.errorMessage);
        assertNotNull(testViewModel.aggregatedCameraState);
    }

    @Test
    public void testRepositoryMockVerification() {
        // 测试Repository Mock验证
        viewModel.startAllCamerasRecording();

        // 验证CameraRepository方法被调用
        verify(mockCameraRepository).startAllRecording(anyString());

        viewModel.stopAllCamerasRecording();

        // 验证CameraRepository方法被调用
        verify(mockCameraRepository).stopAllRecording();
    }
}
