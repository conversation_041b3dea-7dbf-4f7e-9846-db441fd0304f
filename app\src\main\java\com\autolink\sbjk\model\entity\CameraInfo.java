package com.autolink.sbjk.model.entity;

import com.autolink.sbjk.common.constant.CameraConstants;

/**
 * 相机信息实体类
 * 封装相机的基本信息和状态
 */
public class CameraInfo {
    
    private String cameraId;
    private String cameraName;
    private CameraConstants.CameraStatus status;
    private boolean isRecording;
    private boolean isPreviewEnabled;
    private long recordingStartTime;
    private long totalRecordingTime;
    private int width;
    private int height;
    private int bitrate;
    private int frameRate;
    private String currentSegmentPath;
    private int segmentCount;
    private String lastError;
    
    public CameraInfo(String cameraId) {
        this.cameraId = cameraId;
        this.cameraName = CameraConstants.getCameraName(cameraId);
        this.status = CameraConstants.CameraStatus.IDLE;
        this.isRecording = false;
        this.isPreviewEnabled = false;
        this.recordingStartTime = 0;
        this.totalRecordingTime = 0;
        this.width = CameraConstants.DEFAULT_WIDTH;
        this.height = CameraConstants.DEFAULT_HEIGHT;
        this.bitrate = CameraConstants.DEFAULT_BITRATE;
        this.frameRate = CameraConstants.DEFAULT_FRAME_RATE;
        this.segmentCount = 0;
    }
    
    // Getters
    public String getCameraId() { return cameraId; }
    public String getCameraName() { return cameraName; }
    public CameraConstants.CameraStatus getStatus() { return status; }
    public boolean isRecording() { return isRecording; }
    public boolean isPreviewEnabled() { return isPreviewEnabled; }
    public long getRecordingStartTime() { return recordingStartTime; }
    public long getTotalRecordingTime() { return totalRecordingTime; }
    public int getWidth() { return width; }
    public int getHeight() { return height; }
    public int getBitrate() { return bitrate; }
    public int getFrameRate() { return frameRate; }
    public String getCurrentSegmentPath() { return currentSegmentPath; }
    public int getSegmentCount() { return segmentCount; }
    public String getLastError() { return lastError; }
    
    // Setters
    public void setStatus(CameraConstants.CameraStatus status) { this.status = status; }
    public void setRecording(boolean recording) { this.isRecording = recording; }
    public void setPreviewEnabled(boolean previewEnabled) { this.isPreviewEnabled = previewEnabled; }
    public void setRecordingStartTime(long startTime) { this.recordingStartTime = startTime; }
    public void setTotalRecordingTime(long totalTime) { this.totalRecordingTime = totalTime; }
    public void setWidth(int width) { this.width = width; }
    public void setHeight(int height) { this.height = height; }
    public void setBitrate(int bitrate) { this.bitrate = bitrate; }
    public void setFrameRate(int frameRate) { this.frameRate = frameRate; }
    public void setCurrentSegmentPath(String path) { this.currentSegmentPath = path; }
    public void setSegmentCount(int count) { this.segmentCount = count; }
    public void setLastError(String error) { this.lastError = error; }
    
    /**
     * 获取当前录制时长（毫秒）
     */
    public long getCurrentRecordingDuration() {
        if (isRecording && recordingStartTime > 0) {
            return System.currentTimeMillis() - recordingStartTime;
        }
        return 0;
    }
    
    /**
     * 重置录制状态
     */
    public void resetRecordingState() {
        this.isRecording = false;
        this.recordingStartTime = 0;
        this.currentSegmentPath = null;
        this.segmentCount = 0;
        this.lastError = null;
    }
    
    /**
     * 检查相机是否可用
     */
    public boolean isAvailable() {
        return status != CameraConstants.CameraStatus.ERROR && 
               status != CameraConstants.CameraStatus.STOPPED;
    }
    
    /**
     * 检查是否可以开始录制
     */
    public boolean canStartRecording() {
        return status == CameraConstants.CameraStatus.IDLE && !isRecording;
    }
    
    /**
     * 检查是否可以停止录制
     */
    public boolean canStopRecording() {
        return isRecording && (status == CameraConstants.CameraStatus.RECORDING || 
                              status == CameraConstants.CameraStatus.PAUSED);
    }
    
    @Override
    public String toString() {
        return "CameraInfo{" +
                "cameraId='" + cameraId + '\'' +
                ", cameraName='" + cameraName + '\'' +
                ", status=" + status +
                ", isRecording=" + isRecording +
                ", segmentCount=" + segmentCount +
                '}';
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        CameraInfo that = (CameraInfo) obj;
        return cameraId.equals(that.cameraId);
    }
    
    @Override
    public int hashCode() {
        return cameraId.hashCode();
    }
}
