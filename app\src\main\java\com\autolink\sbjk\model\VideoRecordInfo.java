package com.autolink.sbjk.model;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * 录像文件信息模型
 * 轻量级设计，只包含回放列表显示所需的基本信息
 */
public class VideoRecordInfo {
    
    private String cameraDirection;  // 摄像头方向：前视、后视、左视、右视
    private long timestamp;          // 录制时间戳
    private String filePath;         // 完整文件路径
    private String displayTime;      // 格式化显示时间
    private long fileSize;           // 文件大小
    
    // 时间格式化器
    private static final SimpleDateFormat DISPLAY_FORMAT = 
        new SimpleDateFormat("yyyy年MM月dd日 HH:mm:ss", Locale.CHINA);
    
    /**
     * 构造函数 - 从文件路径创建录像信息
     */
    public VideoRecordInfo(String filePath) {
        this.filePath = filePath;
        parseFromFileName();
        updateFileSize();
    }
    
    /**
     * 从文件名解析录像信息
     * 文件名格式：前视_20241219_143025.mp4
     */
    private void parseFromFileName() {
        try {
            String fileName = new File(filePath).getName();
            
            // 移除文件扩展名
            String nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
            
            // 按下划线分割
            String[] parts = nameWithoutExt.split("_");
            
            if (parts.length >= 3) {
                // 解析摄像头方向
                this.cameraDirection = parts[0]; // 前视、后视、左视、右视
                
                // 解析日期和时间
                String dateStr = parts[1];       // 20241219
                String timeStr = parts[2];       // 143025
                
                // 转换为时间戳
                this.timestamp = parseTimestamp(dateStr, timeStr);
                
                // 生成显示时间
                this.displayTime = DISPLAY_FORMAT.format(new Date(timestamp));
            } else {
                // 文件名格式不正确，设置默认值
                this.cameraDirection = null;
                this.timestamp = 0;
                this.displayTime = "未知时间";
            }
        } catch (Exception e) {
            // 解析失败，设置默认值
            this.cameraDirection = null;
            this.timestamp = 0;
            this.displayTime = "解析失败";
        }
    }
    
    /**
     * 高性能时间戳解析
     * 避免使用SimpleDateFormat，提升解析速度
     */
    private long parseTimestamp(String dateStr, String timeStr) {
        try {
            int year = Integer.parseInt(dateStr.substring(0, 4));
            int month = Integer.parseInt(dateStr.substring(4, 6)) - 1; // Calendar月份从0开始
            int day = Integer.parseInt(dateStr.substring(6, 8));
            int hour = Integer.parseInt(timeStr.substring(0, 2));
            int minute = Integer.parseInt(timeStr.substring(2, 4));
            int second = Integer.parseInt(timeStr.substring(4, 6));
            
            // 使用简单的时间戳计算，避免Calendar对象创建
            java.util.Calendar calendar = java.util.Calendar.getInstance();
            calendar.set(year, month, day, hour, minute, second);
            calendar.set(java.util.Calendar.MILLISECOND, 0);
            
            return calendar.getTimeInMillis();
        } catch (Exception e) {
            return System.currentTimeMillis(); // 解析失败返回当前时间
        }
    }
    
    /**
     * 更新文件大小信息
     */
    private void updateFileSize() {
        try {
            File file = new File(filePath);
            this.fileSize = file.exists() ? file.length() : 0;
        } catch (Exception e) {
            this.fileSize = 0;
        }
    }
    
    /**
     * 检查录像信息是否有效
     */
    public boolean isValid() {
        return cameraDirection != null && 
               !cameraDirection.isEmpty() && 
               timestamp > 0 &&
               new File(filePath).exists();
    }
    
    /**
     * 获取格式化的文件大小
     */
    public String getFormattedFileSize() {
        if (fileSize < 1024) {
            return fileSize + " B";
        } else if (fileSize < 1024 * 1024) {
            return String.format("%.1f KB", fileSize / 1024.0);
        } else if (fileSize < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", fileSize / (1024.0 * 1024.0 * 1024.0));
        }
    }
    
    // Getter 方法
    public String getCameraDirection() {
        return cameraDirection;
    }
    
    public long getTimestamp() {
        return timestamp;
    }
    
    public String getFilePath() {
        return filePath;
    }
    
    public String getDisplayTime() {
        return displayTime;
    }
    
    public long getFileSize() {
        return fileSize;
    }
    
    // 用于调试的toString方法
    @Override
    public String toString() {
        return "VideoRecordInfo{" +
                "cameraDirection='" + cameraDirection + '\'' +
                ", displayTime='" + displayTime + '\'' +
                ", filePath='" + filePath + '\'' +
                '}';
    }
    
    // equals和hashCode方法，用于列表去重
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        VideoRecordInfo that = (VideoRecordInfo) obj;
        return filePath != null ? filePath.equals(that.filePath) : that.filePath == null;
    }
    
    @Override
    public int hashCode() {
        return filePath != null ? filePath.hashCode() : 0;
    }
}
