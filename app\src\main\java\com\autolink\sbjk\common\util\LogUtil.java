package com.autolink.sbjk.common.util;

import android.util.Log;

/**
 * 日志工具类
 * 统一管理应用日志输出
 * 标志：sbjklog，等级：i以上
 */
public class LogUtil {

    private static final String APP_TAG = "sbjklog";
    private static boolean isDebugMode = false; // 只允许i级以上日志

    /**
     * Debug级别日志 - 不输出（低于i级）
     */
    public static void d(String tag, String message) {
        // Debug级别不输出，因为要求i级以上
    }

    /**
     * Info级别日志 - 输出
     */
    public static void i(String tag, String message) {
        Log.i(APP_TAG, "[" + tag + "] " + message);
    }

    /**
     * Warning级别日志 - 输出
     */
    public static void w(String tag, String message) {
        Log.w(APP_TAG, "[" + tag + "] " + message);
    }

    public static void w(String tag, String message, Throwable throwable) {
        Log.w(APP_TAG, "[" + tag + "] " + message, throwable);
    }

    /**
     * Error级别日志 - 输出
     */
    public static void e(String tag, String message) {
        Log.e(APP_TAG, "[" + tag + "] " + message);
    }

    public static void e(String tag, String message, Throwable throwable) {
        Log.e(APP_TAG, "[" + tag + "] " + message, throwable);
    }

    /**
     * Verbose级别日志 - 不输出（低于i级）
     */
    public static void v(String tag, String message) {
        // Verbose级别不输出，因为要求i级以上
    }
    
    /**
     * 设置调试模式
     */
    public static void setDebugMode(boolean debug) {
        isDebugMode = debug;
    }
    
    /**
     * 获取调试模式状态
     */
    public static boolean isDebugMode() {
        return isDebugMode;
    }
}
