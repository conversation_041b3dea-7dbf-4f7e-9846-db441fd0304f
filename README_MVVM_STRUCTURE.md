# 车载监控系统MVVM重构目录结构

```
app/src/main/java/com/autolink/sbjk/
├── ui/                                    # View Layer 视图层
│   ├── activity/
│   │   ├── MainActivity.java             # 主界面Activity
│   │   ├── CameraPreviewActivity.java    # 相机预览Activity
│   │   └── SettingsActivity.java         # 设置界面Activity
│   ├── fragment/
│   │   ├── CameraFragment.java           # 相机预览Fragment
│   │   └── ConfigFragment.java           # 配置Fragment
│   ├── adapter/
│   │   └── CameraGridAdapter.java        # 相机网格适配器
│   └── widget/
│       ├── CameraTextureView.java        # 自定义相机预览控件
│       └── RecordingIndicator.java       # 录制状态指示器
│
├── viewmodel/                            # ViewModel Layer 视图模型层
│   ├── MainViewModel.java                # 主界面ViewModel
│   ├── CameraViewModel.java              # 相机管理ViewModel
│   ├── RecordingViewModel.java           # 录制管理ViewModel
│   ├── PreviewViewModel.java             # 预览ViewModel
│   └── SystemViewModel.java              # 系统状态ViewModel
│
├── model/                                # Model Layer 模型层
│   ├── repository/                       # Repository 仓储层
│   │   ├── CameraRepository.java         # 相机数据仓储
│   │   ├── RecordingRepository.java      # 录制数据仓储
│   │   ├── ConfigRepository.java         # 配置数据仓储
│   │   └── FileRepository.java           # 文件管理仓储
│   ├── datasource/                       # DataSource 数据源层
│   │   ├── CameraDataSource.java         # 相机数据源
│   │   ├── RecordingDataSource.java      # 录制数据源
│   │   ├── ConfigDataSource.java         # 配置数据源
│   │   └── FileDataSource.java           # 文件数据源
│   ├── entity/                           # 实体类
│   │   ├── CameraInfo.java               # 相机信息实体
│   │   ├── RecordingSession.java         # 录制会话实体
│   │   ├── VideoSegment.java             # 视频片段实体
│   │   └── SystemStatus.java             # 系统状态实体
│   └── state/                            # 状态类
│       ├── CameraState.java              # 相机状态
│       ├── RecordingState.java           # 录制状态
│       └── UIState.java                  # UI状态
│
├── service/                              # Service Layer 服务层
│   ├── CameraService.java                # 相机服务(重构)
│   ├── RecordingService.java             # 录制服务
│   └── MonitoringService.java            # 监控服务
│
├── core/                                 # Core Layer 核心层
│   ├── camera/
│   │   ├── CameraManager.java            # 相机管理器
│   │   ├── CameraController.java         # 相机控制器
│   │   └── CameraStateManager.java       # 相机状态管理
│   ├── encoder/
│   │   ├── EncoderManager.java           # 编码器管理
│   │   ├── H264Encoder.java              # H264编码器(重构)
│   │   └── EncoderPool.java              # 编码器池
│   ├── resource/
│   │   ├── ResourceManager.java          # 资源管理器
│   │   ├── MemoryManager.java            # 内存管理器
│   │   └── ThreadManager.java            # 线程管理器
│   └── storage/
│       ├── FileManager.java              # 文件管理器
│       ├── SegmentManager.java           # 分段管理器
│       └── StorageMonitor.java           # 存储监控
│
├── common/                               # 通用组件
│   ├── base/
│   │   ├── BaseActivity.java             # Activity基类
│   │   ├── BaseFragment.java             # Fragment基类
│   │   ├── BaseViewModel.java            # ViewModel基类
│   │   └── BaseRepository.java           # Repository基类
│   ├── constant/
│   │   ├── CameraConstants.java          # 相机常量
│   │   ├── RecordingConstants.java       # 录制常量
│   │   └── SystemConstants.java          # 系统常量
│   ├── util/
│   │   ├── LogUtil.java                  # 日志工具
│   │   ├── ThreadUtil.java               # 线程工具
│   │   ├── FileUtil.java                 # 文件工具
│   │   └── PermissionUtil.java           # 权限工具
│   └── exception/
│       ├── CameraException.java          # 相机异常
│       ├── EncodingException.java        # 编码异常
│       └── StorageException.java         # 存储异常
│
└── di/                                   # Dependency Injection 依赖注入
    ├── AppModule.java                    # 应用模块
    ├── CameraModule.java                 # 相机模块
    ├── ServiceModule.java                # 服务模块
    └── RepositoryModule.java             # 仓储模块
```

## 重构实施计划

### 第一阶段：基础架构搭建 (已完成)
✅ 1. 创建基础类和常量定义
✅ 2. 建立实体类和状态类
✅ 3. 实现Repository层接口
✅ 4. 创建ViewModel基类

### 第二阶段：核心功能重构 (进行中)
🔄 1. 重构CameraManager和EncoderManager
✅ 2. 实现MainViewModel
🔄 3. 重构Service层
🔄 4. 完善数据源层

### 第三阶段：UI层重构 (部分完成)
🔄 1. 重构MainActivity (部分完成)
⏳ 2. 优化预览界面
⏳ 3. 完善错误处理和用户反馈

### 第四阶段：测试和优化 (待开始)
⏳ 1. 单元测试
⏳ 2. 集成测试
⏳ 3. 性能优化和内存管理

## 详细实施步骤

### 步骤1: 完成核心组件重构

#### 1.1 扩展CameraRepository支持预览Surface
```java
// 在CameraRepository中添加方法
public void setPreviewSurface(String cameraId, Surface surface);
public void updatePreviewSurface(String cameraId, Surface surface);
```

#### 1.2 重构CameraService
- 使用Repository模式替代直接操作编码器
- 实现服务与Repository的解耦
- 添加服务状态管理

#### 1.3 完善错误处理机制
- 统一异常处理
- 添加重试机制
- 实现故障恢复策略

### 步骤2: UI层完整重构

#### 2.1 完成MainActivity重构
- 移除所有业务逻辑
- 纯粹的UI状态管理
- 完善观察者模式

#### 2.2 创建自定义UI组件
- CameraTextureView: 封装相机预览逻辑
- RecordingIndicator: 录制状态指示器
- 统一的错误提示组件

### 步骤3: 测试策略

#### 3.1 单元测试覆盖
- Repository层测试
- ViewModel层测试
- 数据源层测试
- 工具类测试

#### 3.2 集成测试
- 相机录制流程测试
- 多相机并发测试
- 长时间运行稳定性测试
- 内存泄漏检测

#### 3.3 性能测试
- 启动时间优化
- 内存使用监控
- CPU使用率测试
- 存储I/O性能测试

## 风险控制措施

### 1. 渐进式重构
- 保持原有功能可用
- 分模块逐步替换
- 每个阶段都可独立回滚

### 2. 兼容性保证
- 保留原有API接口
- 新旧代码并存期间的兼容处理
- 数据格式向后兼容

### 3. 测试保障
- 每个重构步骤都有对应测试
- 自动化测试覆盖核心功能
- 性能基准测试

### 4. 监控和日志
- 完善的日志系统
- 性能监控指标
- 错误上报机制

## 预期收益

### 1. 代码质量提升
- 降低代码耦合度
- 提高可测试性
- 增强可维护性

### 2. 功能稳定性
- 更好的错误处理
- 资源管理优化
- 内存泄漏防护

### 3. 开发效率
- 模块化开发
- 单元测试支持
- 更清晰的架构

### 4. 系统性能
- 内存使用优化
- CPU负载均衡
- 更快的响应速度
