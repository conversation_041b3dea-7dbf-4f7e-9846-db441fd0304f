package com.autolink.sbjk.vehicle;

import com.autolink.sbjk.common.util.LogUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * 车辆属性键值定义
 */
public class VehiclePropertyKey {
    private static final String TAG = "VehiclePropertyKey";
    
    // 钥匙状态
    public static final VehiclePropertyKey KeyVehiclePropertyKeySts = new VehiclePropertyKey(2072, "钥匙状态");

    // 档位状态
    public static final VehiclePropertyKey KeyVehiclePropertyGearPosition = new VehiclePropertyKey(1033, "当前档位");

    private static final Map<Integer, VehiclePropertyKey> keyMap = new HashMap<>();
    
    static {
        // 只注册需要监听的属性
        registerKey(KeyVehiclePropertyKeySts);
        registerKey(KeyVehiclePropertyGearPosition);

        LogUtil.i(TAG, "VehiclePropertyKey初始化完成，注册了 " + keyMap.size() + " 个属性");
    }
    
    private static void registerKey(VehiclePropertyKey key) {
        keyMap.put(key.getValue(), key);
    }
    
    private final int value;
    private final String desc;
    
    private VehiclePropertyKey(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }
    
    public int getValue() {
        return value;
    }
    
    public String getDesc() {
        return desc;
    }
    
    public static VehiclePropertyKey valueOf(int value) {
        VehiclePropertyKey key = keyMap.get(value);
        if (key == null) {
            LogUtil.w(TAG, "未找到属性键值: 0x" + Integer.toHexString(value) + " (十进制=" + value + ")");
        }
        return key;
    }
    
    @Override
    public String toString() {
        return desc + " (ID=0x" + Integer.toHexString(value) + ", 十进制=" + value + ")";
    }
}
