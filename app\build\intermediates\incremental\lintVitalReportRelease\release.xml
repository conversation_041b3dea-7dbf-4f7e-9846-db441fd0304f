<variant
    name="release"
    package="com.autolink.sbjk"
    minSdkVersion="30"
    targetSdkVersion="34"
    mergedManifest="build\intermediates\merged_manifest\release\AndroidManifest.xml"
    manifestMergeReport="build\outputs\logs\manifest-merger-release-report.txt"
    proguardFiles="build\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.1.4;proguard-rules.pro"
    partialResultsDir="build\intermediates\lint_vital_partial_results\release\out">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\release\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <mainArtifact
      classOutputs="build\intermediates\javac\release\classes;build\intermediates\compile_and_runtime_not_namespaced_r_class_jar\release\R.jar"
      applicationId="com.autolink.sbjk"
      generatedSourceFolders="build\generated\ap_generated_sources\release\out;build\generated\aidl_source_output_dir\release\out"
      generatedResourceFolders="build\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\transforms-3\23194ec92608fd675ebd4e8fcd4953e4\transformed\D8BackportedDesugaredMethods.txt">
  </mainArtifact>
</variant>
