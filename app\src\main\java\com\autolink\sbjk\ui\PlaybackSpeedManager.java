package com.autolink.sbjk.ui;

import android.media.MediaPlayer;
import android.os.Build;
import android.widget.Button;
import android.widget.VideoView;

import com.autolink.sbjk.common.util.LogUtil;

/**
 * 播放倍速管理器
 * 管理视频播放的倍速功能，支持0.5x到2x的播放速度
 */
public class PlaybackSpeedManager {
    
    private static final String TAG = "PlaybackSpeedManager";
    
    // 支持的播放速度
    private static final float[] SPEED_OPTIONS = {0.5f, 0.75f, 1.0f, 1.25f, 1.5f, 2.0f};
    private static final String[] SPEED_LABELS = {"0.5x", "0.75x", "1.0x", "1.25x", "1.5x", "2.0x"};
    
    // 当前速度索引
    private int currentSpeedIndex = 2; // 默认1.0x
    
    // 倍速变化回调接口
    public interface OnSpeedChangeListener {
        void onSpeedChanged(float speed, String label);
    }
    
    private OnSpeedChangeListener speedChangeListener;
    private Button speedButton;
    private VideoView videoView;
    
    /**
     * 设置倍速变化监听器
     */
    public void setOnSpeedChangeListener(OnSpeedChangeListener listener) {
        this.speedChangeListener = listener;
    }
    
    /**
     * 初始化倍速管理器
     */
    public void init(Button speedButton, VideoView videoView) {
        this.speedButton = speedButton;
        this.videoView = videoView;
        
        // 设置按钮点击事件
        if (speedButton != null) {
            speedButton.setOnClickListener(v -> switchToNextSpeed());
            updateSpeedButtonText();
        }
        
        LogUtil.d(TAG, "倍速管理器初始化完成");
    }
    
    /**
     * 切换到下一个播放速度
     */
    public void switchToNextSpeed() {
        currentSpeedIndex = (currentSpeedIndex + 1) % SPEED_OPTIONS.length;
        float newSpeed = SPEED_OPTIONS[currentSpeedIndex];
        String newLabel = SPEED_LABELS[currentSpeedIndex];
        
        // 应用新的播放速度
        applyPlaybackSpeed(newSpeed);
        
        // 更新按钮文字
        updateSpeedButtonText();
        
        // 通知监听器
        if (speedChangeListener != null) {
            speedChangeListener.onSpeedChanged(newSpeed, newLabel);
        }
        
        LogUtil.d(TAG, "切换播放速度: " + newLabel);
    }
    
    /**
     * 设置指定的播放速度
     */
    public void setPlaybackSpeed(float speed) {
        // 找到最接近的速度选项
        int targetIndex = 2; // 默认1.0x
        float minDiff = Float.MAX_VALUE;
        
        for (int i = 0; i < SPEED_OPTIONS.length; i++) {
            float diff = Math.abs(SPEED_OPTIONS[i] - speed);
            if (diff < minDiff) {
                minDiff = diff;
                targetIndex = i;
            }
        }
        
        currentSpeedIndex = targetIndex;
        applyPlaybackSpeed(SPEED_OPTIONS[currentSpeedIndex]);
        updateSpeedButtonText();
        
        LogUtil.d(TAG, "设置播放速度: " + SPEED_LABELS[currentSpeedIndex]);
    }
    
    /**
     * 应用播放速度到VideoView
     */
    private void applyPlaybackSpeed(float speed) {
        if (videoView == null) {
            LogUtil.w(TAG, "VideoView为空，无法设置播放速度");
            return;
        }

        try {
            // Android API 23+ 支持播放速度调节
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                // 通过反射获取MediaPlayer实例
                java.lang.reflect.Field field = VideoView.class.getDeclaredField("mMediaPlayer");
                field.setAccessible(true);
                MediaPlayer mediaPlayer = (MediaPlayer) field.get(videoView);

                if (mediaPlayer != null) {
                    // 检查MediaPlayer是否处于可设置状态
                    if (mediaPlayer.isPlaying() || isPrepared(mediaPlayer)) {
                        android.media.PlaybackParams params = mediaPlayer.getPlaybackParams();
                        params.setSpeed(speed);
                        mediaPlayer.setPlaybackParams(params);
                        LogUtil.d(TAG, "成功设置播放速度: " + speed + "x");
                    } else {
                        LogUtil.w(TAG, "MediaPlayer未准备就绪，无法设置播放速度");
                    }
                } else {
                    LogUtil.w(TAG, "MediaPlayer为空，无法设置播放速度");
                }
            } else {
                LogUtil.w(TAG, "Android版本过低，不支持播放速度调节 (需要API 23+)");
            }
        } catch (Exception e) {
            LogUtil.e(TAG, "设置播放速度失败: " + e.getMessage(), e);
        }
    }

    /**
     * 检查MediaPlayer是否已准备就绪
     */
    private boolean isPrepared(MediaPlayer mediaPlayer) {
        try {
            // 尝试获取时长来判断是否已准备就绪
            int duration = mediaPlayer.getDuration();
            return duration > 0;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 更新速度按钮文字和样式
     */
    private void updateSpeedButtonText() {
        if (speedButton != null) {
            String label = SPEED_LABELS[currentSpeedIndex];
            speedButton.setText(label);

            // 根据倍速设置不同的视觉效果
            if (currentSpeedIndex == 2) {
                // 正常速度 (1.0x) - 普通样式
                speedButton.setAlpha(0.8f);
            } else {
                // 非正常速度 - 高亮显示
                speedButton.setAlpha(1.0f);
            }
        }
    }
    
    /**
     * 重置到默认速度
     */
    public void resetToNormalSpeed() {
        currentSpeedIndex = 2; // 1.0x
        applyPlaybackSpeed(1.0f);
        updateSpeedButtonText();
        
        if (speedChangeListener != null) {
            speedChangeListener.onSpeedChanged(1.0f, "1.0x");
        }
        
        LogUtil.d(TAG, "重置播放速度为正常速度");
    }
    
    /**
     * 获取当前播放速度
     */
    public float getCurrentSpeed() {
        return SPEED_OPTIONS[currentSpeedIndex];
    }
    
    /**
     * 获取当前速度标签
     */
    public String getCurrentSpeedLabel() {
        return SPEED_LABELS[currentSpeedIndex];
    }
    
    /**
     * 检查是否支持倍速功能
     */
    public static boolean isSpeedControlSupported() {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.M;
    }
    
    /**
     * 获取所有支持的播放速度
     */
    public static float[] getSupportedSpeeds() {
        return SPEED_OPTIONS.clone();
    }
    
    /**
     * 获取所有速度标签
     */
    public static String[] getSpeedLabels() {
        return SPEED_LABELS.clone();
    }
}
