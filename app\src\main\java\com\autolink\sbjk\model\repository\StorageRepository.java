package com.autolink.sbjk.model.repository;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;

import com.autolink.sbjk.common.constant.CameraConstants;
import com.autolink.sbjk.common.util.LogUtil;
import com.autolink.sbjk.core.storage.StorageCleanupManager;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 存储管理仓储
 * 
 * 职责：
 * 1. 管理存储空间检查和清理
 * 2. 提供存储状态的LiveData
 * 3. 定时执行存储检查任务
 * 4. 在录制分段后触发检查
 * 
 * MVVM架构中的Repository层组件
 */
public class StorageRepository {
    
    private static final String TAG = "StorageRepository";
    private static volatile StorageRepository instance;
    
    private final Context context;
    private final StorageCleanupManager cleanupManager;
    private final ExecutorService executorService;
    private final Handler mainHandler;
    
    // LiveData for UI observation
    private final MutableLiveData<StorageCleanupManager.StorageInfo> _storageInfo = new MutableLiveData<>();
    public final LiveData<StorageCleanupManager.StorageInfo> storageInfo = _storageInfo;
    
    private final MutableLiveData<StorageCleanupManager.StorageCleanupResult> _lastCleanupResult = new MutableLiveData<>();
    public final LiveData<StorageCleanupManager.StorageCleanupResult> lastCleanupResult = _lastCleanupResult;
    
    private final MutableLiveData<Boolean> _isChecking = new MutableLiveData<>(false);
    public final LiveData<Boolean> isChecking = _isChecking;
    
    // 定时检查相关
    private Runnable periodicCheckRunnable;
    private boolean isPeriodicCheckEnabled = false;
    
    private StorageRepository(Context context) {
        this.context = context.getApplicationContext();
        this.cleanupManager = new StorageCleanupManager(CameraConstants.DEFAULT_RECORD_PATH);
        this.executorService = Executors.newSingleThreadExecutor();
        this.mainHandler = new Handler(Looper.getMainLooper());
        
        initializePeriodicCheck();
        
        LogUtil.d(TAG, "StorageRepository initialized");
    }
    
    public static StorageRepository getInstance(Context context) {
        if (instance == null) {
            synchronized (StorageRepository.class) {
                if (instance == null) {
                    instance = new StorageRepository(context);
                }
            }
        }
        return instance;
    }
    
    /**
     * 初始化定时检查
     */
    private void initializePeriodicCheck() {
        periodicCheckRunnable = new Runnable() {
            @Override
            public void run() {
                if (isPeriodicCheckEnabled) {
                    checkStorageAsync();
                    // 安排下次检查
                    mainHandler.postDelayed(this, CameraConstants.STORAGE_CHECK_INTERVAL_MS);
                }
            }
        };
    }
    
    /**
     * 启动定时检查
     */
    public void startPeriodicCheck() {
        if (!isPeriodicCheckEnabled) {
            isPeriodicCheckEnabled = true;
            mainHandler.post(periodicCheckRunnable);
            LogUtil.i(TAG, "定时存储检查已启动，间隔: " + CameraConstants.STORAGE_CHECK_INTERVAL_MS / 1000 + "秒");
        }
    }
    
    /**
     * 停止定时检查
     */
    public void stopPeriodicCheck() {
        if (isPeriodicCheckEnabled) {
            isPeriodicCheckEnabled = false;
            mainHandler.removeCallbacks(periodicCheckRunnable);
            LogUtil.i(TAG, "定时存储检查已停止");
        }
    }
    
    /**
     * 异步检查存储空间并清理
     */
    public void checkStorageAsync() {
        executorService.execute(() -> {
            try {
                // 更新检查状态
                _isChecking.postValue(true);
                
                // 更新存储信息
                StorageCleanupManager.StorageInfo info = cleanupManager.getStorageInfo();
                _storageInfo.postValue(info);
                
                // 执行检查和清理
                StorageCleanupManager.StorageCleanupResult result = cleanupManager.checkAndCleanup();
                _lastCleanupResult.postValue(result);
                
                // 如果执行了清理，重新获取存储信息
                if (result.success && result.cleanedFileCount > 0) {
                    StorageCleanupManager.StorageInfo updatedInfo = cleanupManager.getStorageInfo();
                    _storageInfo.postValue(updatedInfo);
                }
                
                LogUtil.d(TAG, "存储检查完成: " + result.message);
                
            } catch (Exception e) {
                LogUtil.e(TAG, "存储检查异常", e);
                StorageCleanupManager.StorageCleanupResult errorResult = 
                    new StorageCleanupManager.StorageCleanupResult(false, "检查异常: " + e.getMessage(), 0, 0);
                _lastCleanupResult.postValue(errorResult);
            } finally {
                _isChecking.postValue(false);
            }
        });
    }
    
    /**
     * 在录制分段后触发检查
     * 由CameraRepository在分段完成后调用
     */
    public void onRecordingSegmentCompleted() {
        LogUtil.d(TAG, "录制分段完成，触发存储检查");
        checkStorageAsync();
    }
    
    /**
     * 手动触发存储检查
     */
    public void manualCheck() {
        LogUtil.i(TAG, "手动触发存储检查");
        checkStorageAsync();
    }
    
    /**
     * 获取当前存储信息（同步）
     */
    public StorageCleanupManager.StorageInfo getCurrentStorageInfo() {
        return cleanupManager.getStorageInfo();
    }
    
    /**
     * 释放资源
     */
    public void release() {
        stopPeriodicCheck();
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
        LogUtil.d(TAG, "StorageRepository released");
    }
}
