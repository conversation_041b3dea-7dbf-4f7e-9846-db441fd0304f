package com.autolink.sbjk.model.entity;

import java.io.File;

/**
 * 视频片段实体类
 * 表示录制过程中的一个视频片段
 */
public class VideoSegment {
    
    private String segmentId;
    private String sessionId;
    private String cameraId;
    private String filePath;
    private long startTime;
    private long endTime;
    private long duration;
    private long fileSize;
    private int segmentIndex;
    private boolean isCompleted;
    private String checksum;
    private String lastError;
    
    public VideoSegment(String segmentId, String sessionId, String cameraId, String filePath, int segmentIndex) {
        this.segmentId = segmentId;
        this.sessionId = sessionId;
        this.cameraId = cameraId;
        this.filePath = filePath;
        this.segmentIndex = segmentIndex;
        this.startTime = System.currentTimeMillis();
        this.endTime = 0;
        this.duration = 0;
        this.fileSize = 0;
        this.isCompleted = false;
    }
    
    // Getters
    public String getSegmentId() { return segmentId; }
    public String getSessionId() { return sessionId; }
    public String getCameraId() { return cameraId; }
    public String getFilePath() { return filePath; }
    public long getStartTime() { return startTime; }
    public long getEndTime() { return endTime; }
    public long getDuration() { return duration; }
    public long getFileSize() { return fileSize; }
    public int getSegmentIndex() { return segmentIndex; }
    public boolean isCompleted() { return isCompleted; }
    public String getChecksum() { return checksum; }
    public String getLastError() { return lastError; }
    
    // Setters
    public void setEndTime(long endTime) { 
        this.endTime = endTime;
        if (startTime > 0) {
            this.duration = endTime - startTime;
        }
    }
    
    public void setDuration(long duration) { this.duration = duration; }
    public void setFileSize(long fileSize) { this.fileSize = fileSize; }
    public void setCompleted(boolean completed) { this.isCompleted = completed; }
    public void setChecksum(String checksum) { this.checksum = checksum; }
    public void setLastError(String error) { this.lastError = error; }
    
    /**
     * 完成片段录制
     */
    public void completeSegment() {
        this.endTime = System.currentTimeMillis();
        this.duration = endTime - startTime;
        this.isCompleted = true;
        
        // 更新文件大小
        updateFileSize();
    }
    
    /**
     * 更新文件大小
     */
    public void updateFileSize() {
        if (filePath != null) {
            File file = new File(filePath);
            if (file.exists()) {
                this.fileSize = file.length();
            }
        }
    }
    
    /**
     * 检查文件是否存在
     */
    public boolean fileExists() {
        if (filePath == null) {
            return false;
        }
        File file = new File(filePath);
        return file.exists() && file.isFile();
    }
    
    /**
     * 获取文件名
     */
    public String getFileName() {
        if (filePath == null) {
            return null;
        }
        return new File(filePath).getName();
    }
    
    /**
     * 获取文件目录
     */
    public String getFileDirectory() {
        if (filePath == null) {
            return null;
        }
        return new File(filePath).getParent();
    }
    
    /**
     * 检查片段是否有效
     */
    public boolean isValid() {
        return segmentId != null && !segmentId.isEmpty() &&
               sessionId != null && !sessionId.isEmpty() &&
               cameraId != null && !cameraId.isEmpty() &&
               filePath != null && !filePath.isEmpty() &&
               startTime > 0;
    }
    
    /**
     * 获取当前录制时长（如果正在录制）
     */
    public long getCurrentDuration() {
        if (!isCompleted && startTime > 0) {
            return System.currentTimeMillis() - startTime;
        }
        return duration;
    }
    
    /**
     * 格式化文件大小显示
     */
    public String getFormattedFileSize() {
        if (fileSize < 1024) {
            return fileSize + " B";
        } else if (fileSize < 1024 * 1024) {
            return String.format("%.1f KB", fileSize / 1024.0);
        } else if (fileSize < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", fileSize / (1024.0 * 1024.0 * 1024.0));
        }
    }
    
    /**
     * 格式化时长显示
     */
    public String getFormattedDuration() {
        long durationSeconds = getCurrentDuration() / 1000;
        long hours = durationSeconds / 3600;
        long minutes = (durationSeconds % 3600) / 60;
        long seconds = durationSeconds % 60;
        
        if (hours > 0) {
            return String.format("%02d:%02d:%02d", hours, minutes, seconds);
        } else {
            return String.format("%02d:%02d", minutes, seconds);
        }
    }
    
    /**
     * 删除片段文件
     */
    public boolean deleteFile() {
        if (filePath != null) {
            File file = new File(filePath);
            if (file.exists()) {
                return file.delete();
            }
        }
        return false;
    }
    
    @Override
    public String toString() {
        return "VideoSegment{" +
                "segmentId='" + segmentId + '\'' +
                ", cameraId='" + cameraId + '\'' +
                ", segmentIndex=" + segmentIndex +
                ", duration=" + duration +
                ", fileSize=" + fileSize +
                ", isCompleted=" + isCompleted +
                '}';
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        VideoSegment that = (VideoSegment) obj;
        return segmentId.equals(that.segmentId);
    }
    
    @Override
    public int hashCode() {
        return segmentId.hashCode();
    }
}
