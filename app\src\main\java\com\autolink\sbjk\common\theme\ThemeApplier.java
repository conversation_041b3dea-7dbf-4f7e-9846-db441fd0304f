package com.autolink.sbjk.common.theme;

import android.view.View;
import android.view.Window;
import android.widget.Button;
import android.widget.TextView;
import androidx.cardview.widget.CardView;
import com.autolink.sbjk.common.util.LogUtil;

/**
 * 主题应用器 - 统一的UI组件主题应用逻辑
 * 
 * 职责：
 * 1. 提供统一的UI组件主题应用方法
 * 2. 替换原有分散在各个类中的颜色设置逻辑
 * 3. 确保主题应用的一致性和可维护性
 * 
 * 设计原则：
 * - 静态方法：无状态，可直接调用
 * - 空值安全：所有方法都进行空值检查
 * - 异常安全：捕获并记录异常，不影响应用运行
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-04
 */
public class ThemeApplier {
    
    private static final String TAG = "ThemeApplier";
    
    /**
     * 应用主题到TextView
     * 
     * 替换原有的手动颜色设置：
     * - tvDatetimeDisplay.setTextColor(isDarkMode ? Color.WHITE : Color.BLACK)
     * - sentryFunctionText.setTextColor(isDarkMode ? Color.WHITE : Color.BLACK)
     * 
     * @param textView 目标TextView
     * @param colors 主题颜色配置
     * @param isPrimary 是否为主要文本（true=主要文本，false=次要文本）
     */
    public static void applyToTextView(TextView textView, ThemeManager.ThemeColors colors, boolean isPrimary) {
        if (textView == null || colors == null) {
            LogUtil.w(TAG, "TextView or colors is null, skipping theme application");
            return;
        }
        
        try {
            int textColor = isPrimary ? colors.textPrimary : colors.textSecondary;
            textView.setTextColor(textColor);
            
            LogUtil.d(TAG, "Applied theme to TextView: " + textView.getClass().getSimpleName() + 
                     ", color: " + Integer.toHexString(textColor));
        } catch (Exception e) {
            LogUtil.e(TAG, "Error applying theme to TextView", e);
        }
    }
    
    /**
     * 应用主题到按钮
     * 
     * 替换原有FilterButtonManager和MainActivity中的按钮颜色设置：
     * - button.setTextColor(isDarkMode ? Color.WHITE : Color.BLACK)
     * - button.setTextColor(isSelected ? colors.buttonTextSelected : colors.buttonTextUnselected)
     * 
     * @param button 目标按钮
     * @param colors 主题颜色配置
     * @param isSelected 是否为选中状态
     */
    public static void applyToButton(Button button, ThemeManager.ThemeColors colors, boolean isSelected) {
        if (button == null || colors == null) {
            LogUtil.w(TAG, "Button or colors is null, skipping theme application");
            return;
        }
        
        try {
            int textColor = isSelected ? colors.buttonTextSelected : colors.buttonTextUnselected;
            button.setTextColor(textColor);
            
            LogUtil.d(TAG, "Applied theme to Button: " + button.getText() + 
                     ", selected: " + isSelected + ", color: " + Integer.toHexString(textColor));
        } catch (Exception e) {
            LogUtil.e(TAG, "Error applying theme to Button", e);
        }
    }
    
    /**
     * 应用主题到背景View
     * 
     * 替换原有MainActivity中的背景颜色设置：
     * - leftControlPanel.setBackgroundColor(isDarkMode ? Color.BLACK : Color.parseColor("#DEE2E5"))
     * - previewArea.setBackgroundColor(isDarkMode ? Color.BLACK : Color.parseColor("#DEE2E5"))
     * 
     * @param view 目标View
     * @param colors 主题颜色配置
     * @param isPrimary 是否为主要背景（true=主要背景，false=次要背景）
     */
    public static void applyToBackground(View view, ThemeManager.ThemeColors colors, boolean isPrimary) {
        if (view == null || colors == null) {
            LogUtil.w(TAG, "View or colors is null, skipping theme application");
            return;
        }
        
        try {
            int backgroundColor = isPrimary ? colors.backgroundPrimary : colors.backgroundSecondary;
            view.setBackgroundColor(backgroundColor);
            
            LogUtil.d(TAG, "Applied theme to background View: " + view.getClass().getSimpleName() + 
                     ", primary: " + isPrimary + ", color: " + Integer.toHexString(backgroundColor));
        } catch (Exception e) {
            LogUtil.e(TAG, "Error applying theme to background View", e);
        }
    }
    
    /**
     * 应用主题到CardView容器
     * 
     * 替换原有MainActivity.updateCameraContainerBackgrounds中的逻辑：
     * - container.setCardBackgroundColor(isDarkMode ? Color.parseColor("#1A1A1A") : Color.parseColor("#F5F5F5"))
     * 
     * @param cardView 目标CardView
     * @param colors 主题颜色配置
     */
    public static void applyToCardView(CardView cardView, ThemeManager.ThemeColors colors) {
        if (cardView == null || colors == null) {
            LogUtil.w(TAG, "CardView or colors is null, skipping theme application");
            return;
        }
        
        try {
            cardView.setCardBackgroundColor(colors.containerBackground);
            
            LogUtil.d(TAG, "Applied theme to CardView, color: " + 
                     Integer.toHexString(colors.containerBackground));
        } catch (Exception e) {
            LogUtil.e(TAG, "Error applying theme to CardView", e);
        }
    }
    
    /**
     * 应用主题到页面按钮（Button类型）
     *
     * 替换原有MainActivity.updatePageButtonState中的逻辑：
     * - activeButton.setSelected(true); inactiveButton.setSelected(false);
     * - activeButton.setTextColor(isDarkMode ? Color.WHITE : Color.BLACK);
     * - inactiveButton.setTextColor(isDarkMode ? Color.parseColor("#CCCCCC") : Color.parseColor("#808080"));
     *
     * @param activeButton 当前激活的按钮
     * @param inactiveButton 当前未激活的按钮
     * @param colors 主题颜色配置
     */
    public static void applyToPageButtons(Button activeButton, Button inactiveButton, ThemeManager.ThemeColors colors) {
        if (activeButton == null || inactiveButton == null || colors == null) {
            LogUtil.w(TAG, "Page buttons or colors is null, skipping theme application");
            return;
        }

        try {
            // 设置选中状态
            activeButton.setSelected(true);
            inactiveButton.setSelected(false);

            // 应用颜色
            activeButton.setTextColor(colors.buttonTextSelected);
            inactiveButton.setTextColor(colors.buttonTextUnselected);

            LogUtil.d(TAG, "Applied theme to page buttons - Active: " + activeButton.getText() +
                     ", Inactive: " + inactiveButton.getText());
        } catch (Exception e) {
            LogUtil.e(TAG, "Error applying theme to page buttons", e);
        }
    }

    /**
     * 应用主题到页面按钮（TextView类型）
     *
     * 专门处理MainActivity中的页面切换按钮（btnSentinelMonitor, btnVideoPlayback）
     * 这些按钮实际上是TextView类型
     *
     * @param activeTextView 当前激活的TextView按钮
     * @param inactiveTextView 当前未激活的TextView按钮
     * @param colors 主题颜色配置
     */
    public static void applyToPageTextViews(TextView activeTextView, TextView inactiveTextView, ThemeManager.ThemeColors colors) {
        if (activeTextView == null || inactiveTextView == null || colors == null) {
            LogUtil.w(TAG, "Page TextViews or colors is null, skipping theme application");
            return;
        }

        try {
            // 设置选中状态
            activeTextView.setSelected(true);
            inactiveTextView.setSelected(false);

            // 应用颜色
            activeTextView.setTextColor(colors.buttonTextSelected);
            inactiveTextView.setTextColor(colors.buttonTextUnselected);

            LogUtil.d(TAG, "Applied theme to page TextViews - Active: " + activeTextView.getText() +
                     ", Inactive: " + inactiveTextView.getText());
        } catch (Exception e) {
            LogUtil.e(TAG, "Error applying theme to page TextViews", e);
        }
    }
    
    /**
     * 应用主题到状态栏
     * 
     * 替换原有MainActivity.setupTheme中的状态栏设置逻辑：
     * - window.setStatusBarColor(isDarkMode ? Color.BLACK : Color.parseColor("#DEE2E5"))
     * - window.getDecorView().setSystemUiVisibility(...)
     * 
     * @param window 窗口对象
     * @param colors 主题颜色配置
     * @param isDarkMode 是否为深色模式
     */
    public static void applyToStatusBar(Window window, ThemeManager.ThemeColors colors, boolean isDarkMode) {
        if (window == null || colors == null) {
            LogUtil.w(TAG, "Window or colors is null, skipping status bar theme application");
            return;
        }
        
        try {
            window.addFlags(android.view.WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            window.setStatusBarColor(colors.statusBarColor);
            
            if (isDarkMode) {
                // 深色模式：清除浅色状态栏标志
                window.getDecorView().setSystemUiVisibility(0);
            } else {
                // 浅色模式：设置浅色状态栏标志
                window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
            }
            
            LogUtil.d(TAG, "Applied theme to status bar, dark mode: " + isDarkMode + 
                     ", color: " + Integer.toHexString(colors.statusBarColor));
        } catch (Exception e) {
            LogUtil.e(TAG, "Error applying theme to status bar", e);
        }
    }
    
    /**
     * 批量应用主题到多个TextView
     * 
     * 便捷方法，用于批量处理多个TextView
     * 
     * @param colors 主题颜色配置
     * @param isPrimary 是否为主要文本
     * @param textViews 要应用主题的TextView数组
     */
    public static void applyToTextViews(ThemeManager.ThemeColors colors, boolean isPrimary, TextView... textViews) {
        if (colors == null || textViews == null) {
            LogUtil.w(TAG, "Colors or textViews is null, skipping batch theme application");
            return;
        }
        
        for (TextView textView : textViews) {
            applyToTextView(textView, colors, isPrimary);
        }
        
        LogUtil.d(TAG, "Applied theme to " + textViews.length + " TextViews");
    }
    
    /**
     * 批量应用主题到多个按钮
     * 
     * 便捷方法，用于批量处理多个按钮
     * 
     * @param colors 主题颜色配置
     * @param isSelected 是否为选中状态
     * @param buttons 要应用主题的按钮数组
     */
    public static void applyToButtons(ThemeManager.ThemeColors colors, boolean isSelected, Button... buttons) {
        if (colors == null || buttons == null) {
            LogUtil.w(TAG, "Colors or buttons is null, skipping batch theme application");
            return;
        }
        
        for (Button button : buttons) {
            applyToButton(button, colors, isSelected);
        }
        
        LogUtil.d(TAG, "Applied theme to " + buttons.length + " Buttons");
    }
    
    /**
     * 批量应用主题到多个背景View
     * 
     * 便捷方法，用于批量处理多个背景View
     * 
     * @param colors 主题颜色配置
     * @param isPrimary 是否为主要背景
     * @param views 要应用主题的View数组
     */
    public static void applyToBackgrounds(ThemeManager.ThemeColors colors, boolean isPrimary, View... views) {
        if (colors == null || views == null) {
            LogUtil.w(TAG, "Colors or views is null, skipping batch theme application");
            return;
        }
        
        for (View view : views) {
            applyToBackground(view, colors, isPrimary);
        }
        
        LogUtil.d(TAG, "Applied theme to " + views.length + " background Views");
    }
    
    /**
     * 批量应用主题到多个CardView
     * 
     * 便捷方法，用于批量处理多个CardView
     * 
     * @param colors 主题颜色配置
     * @param cardViews 要应用主题的CardView数组
     */
    public static void applyToCardViews(ThemeManager.ThemeColors colors, CardView... cardViews) {
        if (colors == null || cardViews == null) {
            LogUtil.w(TAG, "Colors or cardViews is null, skipping batch theme application");
            return;
        }
        
        for (CardView cardView : cardViews) {
            applyToCardView(cardView, colors);
        }
        
        LogUtil.d(TAG, "Applied theme to " + cardViews.length + " CardViews");
    }
}
