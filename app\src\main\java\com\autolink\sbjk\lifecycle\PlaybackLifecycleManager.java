package com.autolink.sbjk.lifecycle;

import android.app.Activity;
import android.app.Application;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelStoreOwner;

import com.autolink.sbjk.adapter.VideoListAdapter;
import com.autolink.sbjk.common.util.LogUtil;
import com.autolink.sbjk.repository.PlaybackRepository;
import com.autolink.sbjk.scanner.QuickVideoScanner;
import com.autolink.sbjk.ui.FilterButtonManager;
import com.autolink.sbjk.ui.PlaybackSpeedManager;
import com.autolink.sbjk.ui.TimePickerManager;
import com.autolink.sbjk.viewmodel.PlaybackViewModel;
import com.autolink.sbjk.viewmodel.PlaybackViewModelFactory;

/**
 * 录像回放功能生命周期管理器
 * 
 * 职责：
 * 1. 管理录像回放功能的完整生命周期
 * 2. 确保与哨兵监控功能完全隔离
 * 3. 在适当时机自动清理所有资源
 * 4. 监听应用前后台状态变化
 */
public class PlaybackLifecycleManager implements Application.ActivityLifecycleCallbacks {
    
    private static final String TAG = "PlaybackLifecycleManager";
    
    // 单例实例
    private static volatile PlaybackLifecycleManager instance;
    
    // 生命周期状态
    private boolean isPlaybackActive = false;
    private boolean isAppInBackground = false;
    
    // 回放功能组件（只在生命周期内存在）
    private PlaybackViewModel playbackViewModel;
    private VideoListAdapter videoListAdapter;
    private FilterButtonManager filterButtonManager;
    private TimePickerManager timePickerManager;
    private PlaybackSpeedManager playbackSpeedManager;
    private QuickVideoScanner videoScanner;

    // Observer清理标记
    private boolean observersRegistered = false;

    // 生命周期结束回调接口
    public interface OnLifecycleEndListener {
        void onLifecycleEnd(String reason);
    }

    private OnLifecycleEndListener lifecycleEndListener;
    
    // 清理Handler
    private final Handler cleanupHandler = new Handler(Looper.getMainLooper());
    private Runnable cleanupRunnable;
    
    // 宿主Activity引用（弱引用，避免内存泄漏）
    private Activity hostActivity;

    // Application引用，用于注销生命周期监听
    private Application application;
    
    private PlaybackLifecycleManager() {
        // 私有构造函数
    }
    
    /**
     * 获取单例实例
     */
    public static PlaybackLifecycleManager getInstance() {
        if (instance == null) {
            synchronized (PlaybackLifecycleManager.class) {
                if (instance == null) {
                    instance = new PlaybackLifecycleManager();
                }
            }
        }
        return instance;
    }
    
    /**
     * 注册应用生命周期监听
     */
    public void registerAppLifecycle(Application application) {
        this.application = application;
        application.registerActivityLifecycleCallbacks(this);
        LogUtil.d(TAG, "应用生命周期监听已注册");
    }

    /**
     * 注销应用生命周期监听
     */
    public void unregisterAppLifecycle() {
        if (application != null) {
            application.unregisterActivityLifecycleCallbacks(this);
            application = null;
            LogUtil.d(TAG, "应用生命周期监听已注销");
        }
    }

    /**
     * 设置生命周期结束监听器
     */
    public void setOnLifecycleEndListener(OnLifecycleEndListener listener) {
        this.lifecycleEndListener = listener;
    }
    
    /**
     * 开始录像回放生命周期
     */
    public void startPlaybackLifecycle(Activity activity) {
        if (isPlaybackActive) {
            LogUtil.w(TAG, "录像回放生命周期已经激活，忽略重复启动");
            return;
        }

        LogUtil.i(TAG, "开始录像回放生命周期");

        this.hostActivity = activity;

        try {
            // 初始化所有回放组件
            initializePlaybackComponents(activity);

            // 只有初始化成功后才标记为激活
            this.isPlaybackActive = true;
            LogUtil.i(TAG, "录像回放生命周期启动完成");

        } catch (Exception e) {
            LogUtil.e(TAG, "录像回放生命周期启动失败", e);
            // 清理部分初始化的资源
            cleanupAllResources();
            this.hostActivity = null;
            throw e; // 重新抛出异常，让调用者知道初始化失败
        }
    }
    
    /**
     * 结束录像回放生命周期
     */
    public void endPlaybackLifecycle(String reason) {
        if (!isPlaybackActive) {
            LogUtil.d(TAG, "录像回放生命周期未激活，无需结束");
            return;
        }
        
        LogUtil.i(TAG, "结束录像回放生命周期，原因: " + reason);
        
        // 立即标记为非激活状态
        isPlaybackActive = false;
        
        // 清理所有资源
        cleanupAllResources();
        
        // 清除宿主Activity引用
        hostActivity = null;

        // 通知监听器生命周期结束
        if (lifecycleEndListener != null) {
            lifecycleEndListener.onLifecycleEnd(reason);
        }

        LogUtil.i(TAG, "录像回放生命周期结束完成");
    }
    
    /**
     * 初始化回放组件
     */
    private void initializePlaybackComponents(Activity activity) {
        try {
            // 初始化ViewModel（使用Factory）
            if (activity instanceof ViewModelStoreOwner) {
                PlaybackViewModelFactory factory = new PlaybackViewModelFactory(activity);
                playbackViewModel = new ViewModelProvider((ViewModelStoreOwner) activity, factory)
                    .get(PlaybackViewModel.class);
            }

            // 初始化适配器
            videoListAdapter = new VideoListAdapter();
            
            // 初始化管理器
            filterButtonManager = new FilterButtonManager();
            timePickerManager = new TimePickerManager(activity);
            playbackSpeedManager = new PlaybackSpeedManager();
            
            // 初始化扫描器
            videoScanner = new QuickVideoScanner();
            
            LogUtil.d(TAG, "回放组件初始化完成");
            
        } catch (Exception e) {
            LogUtil.e(TAG, "初始化回放组件失败", e);
            // 如果初始化失败，立即清理
            cleanupAllResources();
        }
    }
    
    /**
     * 清理所有资源
     */
    private void cleanupAllResources() {
        try {
            // 清理Observer
            cleanupObservers();

            // 清理扫描器
            if (videoScanner != null) {
                videoScanner.shutdown();
                videoScanner = null;
            }

            // 清理Repository
            PlaybackRepository repository = PlaybackRepository.getInstance();
            if (repository != null) {
                repository.cleanup();
            }

            // 清理管理器（这些对象没有特殊清理需求，直接置空）
            filterButtonManager = null;
            timePickerManager = null;
            playbackSpeedManager = null;

            // 清理适配器
            videoListAdapter = null;

            // 清理ViewModel状态
            if (playbackViewModel != null) {
                playbackViewModel.cleanup();
                playbackViewModel = null;
            }

            // 取消待执行的清理任务
            if (cleanupRunnable != null) {
                cleanupHandler.removeCallbacks(cleanupRunnable);
                cleanupRunnable = null;
            }

            LogUtil.d(TAG, "所有回放资源清理完成");

        } catch (Exception e) {
            LogUtil.e(TAG, "清理回放资源时出错", e);
        }
    }

    /**
     * 清理Observer
     */
    private void cleanupObservers() {
        if (observersRegistered && playbackViewModel != null && hostActivity != null) {
            try {
                // 确保hostActivity是LifecycleOwner
                if (hostActivity instanceof LifecycleOwner) {
                    LifecycleOwner lifecycleOwner = (LifecycleOwner) hostActivity;

                    // 移除所有Observer
                    playbackViewModel.getVideoList().removeObservers(lifecycleOwner);
                    playbackViewModel.getLoadingState().removeObservers(lifecycleOwner);
                    playbackViewModel.getErrorMessage().removeObservers(lifecycleOwner);
                    playbackViewModel.getSelectedVideo().removeObservers(lifecycleOwner);

                    observersRegistered = false;
                    LogUtil.d(TAG, "Observer清理完成");
                } else {
                    LogUtil.w(TAG, "hostActivity不是LifecycleOwner，无法清理Observer");
                }

            } catch (Exception e) {
                LogUtil.e(TAG, "清理Observer时出错", e);
            }
        }
    }

    /**
     * 标记Observer已注册
     */
    public void markObserversRegistered() {
        observersRegistered = true;
    }
    
    /**
     * 检查回放功能是否激活
     */
    public boolean isPlaybackActive() {
        return isPlaybackActive;
    }
    
    /**
     * 获取回放组件（仅在生命周期内有效）
     */
    public PlaybackViewModel getPlaybackViewModel() {
        return isPlaybackActive ? playbackViewModel : null;
    }
    
    public VideoListAdapter getVideoListAdapter() {
        return isPlaybackActive ? videoListAdapter : null;
    }
    
    public FilterButtonManager getFilterButtonManager() {
        return isPlaybackActive ? filterButtonManager : null;
    }
    
    public TimePickerManager getTimePickerManager() {
        return isPlaybackActive ? timePickerManager : null;
    }
    
    public PlaybackSpeedManager getPlaybackSpeedManager() {
        return isPlaybackActive ? playbackSpeedManager : null;
    }
    
    // ========== Application.ActivityLifecycleCallbacks 实现 ==========
    
    @Override
    public void onActivityCreated(@NonNull Activity activity, @Nullable Bundle savedInstanceState) {
        // 不需要处理
    }
    
    @Override
    public void onActivityStarted(@NonNull Activity activity) {
        // 只有当宿主Activity回到前台时才处理
        if (activity == hostActivity && isAppInBackground) {
            isAppInBackground = false;
            LogUtil.d(TAG, "宿主Activity回到前台");
        }
    }
    
    @Override
    public void onActivityResumed(@NonNull Activity activity) {
        // 不需要处理
    }
    
    @Override
    public void onActivityPaused(@NonNull Activity activity) {
        // 不需要处理
    }
    
    @Override
    public void onActivityStopped(@NonNull Activity activity) {
        // 只有当宿主Activity进入后台时才处理
        if (activity == hostActivity) {
            isAppInBackground = true;
            LogUtil.d(TAG, "宿主Activity进入后台");

            // 如果录像回放功能激活，立即结束其生命周期
            if (isPlaybackActive) {
                endPlaybackLifecycle("应用进入后台");
            }
        }
    }
    
    @Override
    public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle outState) {
        // 不保存录像回放状态（确保状态不恢复）
    }
    
    @Override
    public void onActivityDestroyed(@NonNull Activity activity) {
        // 如果是宿主Activity被销毁，结束回放生命周期
        if (activity == hostActivity) {
            endPlaybackLifecycle("宿主Activity销毁");
        }
    }
}
