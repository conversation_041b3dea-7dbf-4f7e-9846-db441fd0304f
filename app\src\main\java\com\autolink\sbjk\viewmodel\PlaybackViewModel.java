package com.autolink.sbjk.viewmodel;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import com.autolink.sbjk.model.VideoRecordInfo;
import com.autolink.sbjk.repository.PlaybackRepository;
import com.autolink.sbjk.common.util.LogUtil;

import java.util.List;

/**
 * 回放功能的ViewModel
 * 处理UI逻辑和数据绑定，连接View和Repository
 */
public class PlaybackViewModel extends ViewModel {
    
    private static final String TAG = "PlaybackViewModel";
    
    // 数据仓库
    private final PlaybackRepository repository;
    
    // UI状态数据
    private final MutableLiveData<VideoRecordInfo> selectedVideoLiveData;
    private final MutableLiveData<String> selectedCameraFilterLiveData;
    private final MutableLiveData<String> selectedTimeFilterLiveData;
    private final MutableLiveData<Boolean> isPlayingLiveData;
    
    // 筛选选项
    private final String[] cameraOptions = {"全部", "前视", "后视", "左视", "右视"};
    private final String[] timeOptions = {"全部", "月", "日", "时"};
    
    public PlaybackViewModel() {
        this.repository = PlaybackRepository.getInstance();
        this.selectedVideoLiveData = new MutableLiveData<>();
        this.selectedCameraFilterLiveData = new MutableLiveData<>("全部");
        this.selectedTimeFilterLiveData = new MutableLiveData<>("全部");
        this.isPlayingLiveData = new MutableLiveData<>(false);
        
        LogUtil.d(TAG, "PlaybackViewModel 初始化完成");
    }
    
    /**
     * 获取录像列表数据
     */
    public LiveData<List<VideoRecordInfo>> getVideoList() {
        return repository.getFilteredVideos();
    }
    
    /**
     * 获取所有录像数据
     */
    public LiveData<List<VideoRecordInfo>> getAllVideos() {
        return repository.getAllVideos();
    }
    
    /**
     * 获取加载状态
     */
    public LiveData<Boolean> getLoadingState() {
        return repository.getLoadingState();
    }
    
    /**
     * 获取错误信息
     */
    public LiveData<String> getErrorMessage() {
        return repository.getErrorMessage();
    }
    
    /**
     * 获取当前选中的录像
     */
    public LiveData<VideoRecordInfo> getSelectedVideo() {
        return selectedVideoLiveData;
    }
    
    /**
     * 获取当前摄像头筛选
     */
    public LiveData<String> getSelectedCameraFilter() {
        return selectedCameraFilterLiveData;
    }
    
    /**
     * 获取当前时间筛选
     */
    public LiveData<String> getSelectedTimeFilter() {
        return selectedTimeFilterLiveData;
    }
    
    /**
     * 获取播放状态
     */
    public LiveData<Boolean> getPlayingState() {
        return isPlayingLiveData;
    }
    
    /**
     * 加载录像列表
     */
    public void loadVideos() {
        LogUtil.d(TAG, "开始加载录像列表");
        repository.loadAllVideos();
    }
    
    /**
     * 刷新录像列表
     */
    public void refreshVideos() {
        LogUtil.d(TAG, "刷新录像列表");
        repository.refreshVideos();
    }
    
    /**
     * 选择录像文件进行播放
     */
    public void selectVideo(VideoRecordInfo video) {
        if (video != null) {
            selectedVideoLiveData.setValue(video);
            LogUtil.d(TAG, "选择录像: " + video.getCameraDirection() + " " + video.getDisplayTime());
        }
    }
    
    /**
     * 设置摄像头筛选
     */
    public void setCameraFilter(String cameraFilter) {
        if (cameraFilter != null && !cameraFilter.equals(selectedCameraFilterLiveData.getValue())) {
            selectedCameraFilterLiveData.setValue(cameraFilter);
            repository.filterByCamera(cameraFilter);
            LogUtil.d(TAG, "设置摄像头筛选: " + cameraFilter);
        }
    }
    
    /**
     * 设置精确时间筛选
     */
    public void setTimeFilter(int month, int day, int hour) {
        String timeDesc = formatTimeFilter(month, day, hour);
        selectedTimeFilterLiveData.setValue(timeDesc);
        repository.filterByTime(month, day, hour);
        LogUtil.d(TAG, "设置时间筛选: " + timeDesc);
    }

    /**
     * 重置时间筛选
     */
    public void resetTimeFilter() {
        selectedTimeFilterLiveData.setValue("全部");
        repository.resetTimeFilter();
        LogUtil.d(TAG, "重置时间筛选");
    }

    /**
     * 格式化时间筛选描述
     */
    private String formatTimeFilter(int month, int day, int hour) {
        if (month == -1 && day == -1 && hour == -1) {
            return "全部";
        }

        StringBuilder desc = new StringBuilder();
        if (month != -1) {
            desc.append(month).append("月");
        }
        if (day != -1) {
            desc.append(day).append("日");
        }
        if (hour != -1) {
            desc.append(hour).append("时");
        }

        return desc.toString();
    }
    
    /**
     * 获取摄像头筛选选项
     */
    public String[] getCameraOptions() {
        return cameraOptions;
    }
    
    /**
     * 获取时间筛选选项
     */
    public String[] getTimeOptions() {
        return timeOptions;
    }
    
    /**
     * 开始播放
     */
    public void startPlayback() {
        isPlayingLiveData.setValue(true);
        LogUtil.d(TAG, "开始播放");
    }
    
    /**
     * 暂停播放
     */
    public void pausePlayback() {
        isPlayingLiveData.setValue(false);
        LogUtil.d(TAG, "暂停播放");
    }
    
    /**
     * 停止播放
     */
    public void stopPlayback() {
        isPlayingLiveData.setValue(false);
        selectedVideoLiveData.setValue(null);
        LogUtil.d(TAG, "停止播放");
    }
    
    /**
     * 切换播放状态
     */
    public void togglePlayback() {
        Boolean currentState = isPlayingLiveData.getValue();
        if (currentState != null && currentState) {
            pausePlayback();
        } else {
            startPlayback();
        }
    }
    
    /**
     * 添加新录像（用于实时更新）
     */
    public void addNewVideo(VideoRecordInfo newVideo) {
        repository.addNewVideo(newVideo);
    }
    
    /**
     * 清除错误信息
     */
    public void clearError() {
        repository.clearError();
    }

    /**
     * 清理ViewModel状态（生命周期结束时调用）
     */
    public void cleanup() {
        // 清除选中的视频
        selectedVideoLiveData.setValue(null);

        // 重置播放状态
        isPlayingLiveData.setValue(false);

        // 重置筛选条件
        selectedCameraFilterLiveData.setValue("全部");
        selectedTimeFilterLiveData.setValue("全部");

        LogUtil.d(TAG, "PlaybackViewModel状态已清理");
    }
    
    /**
     * 获取当前筛选状态的描述
     */
    public String getFilterDescription() {
        String camera = selectedCameraFilterLiveData.getValue();
        String time = selectedTimeFilterLiveData.getValue();
        
        if ("全部".equals(camera) && "全部".equals(time)) {
            return "显示所有录像";
        } else if ("全部".equals(camera)) {
            return "显示" + time + "的录像";
        } else if ("全部".equals(time)) {
            return "显示" + camera + "的录像";
        } else {
            return "显示" + camera + time + "的录像";
        }
    }
    
    /**
     * 检查是否有录像文件
     */
    public boolean hasVideos() {
        List<VideoRecordInfo> videos = getVideoList().getValue();
        return videos != null && !videos.isEmpty();
    }
    
    /**
     * 获取录像文件数量
     */
    public int getVideoCount() {
        List<VideoRecordInfo> videos = getVideoList().getValue();
        return videos != null ? videos.size() : 0;
    }
    
    /**
     * 重置所有筛选条件
     */
    public void resetFilters() {
        setCameraFilter("全部");
        resetTimeFilter();
        LogUtil.d(TAG, "重置筛选条件");
    }
    
    /**
     * ViewModel销毁时的清理工作
     */
    @Override
    protected void onCleared() {
        super.onCleared();
        repository.cleanup();
        LogUtil.d(TAG, "PlaybackViewModel 已清理");
    }
}
