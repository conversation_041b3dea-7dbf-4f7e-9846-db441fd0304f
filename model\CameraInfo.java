package com.autolink.sbjk.model;

import androidx.annotation.NonNull;
import java.util.Objects;

/**
 * 摄像头信息数据模型
 * 封装摄像头的基本信息和状态
 */
public class CameraInfo {
    
    // 摄像头位置枚举
    public enum Position {
        FRONT("26", "前摄像头"),
        BACK("30", "后摄像头"), 
        LEFT("34", "左摄像头"),
        RIGHT("38", "右摄像头");
        
        private final String id;
        private final String displayName;
        
        Position(String id, String displayName) {
            this.id = id;
            this.displayName = displayName;
        }
        
        public String getId() { return id; }
        public String getDisplayName() { return displayName; }
        
        public static Position fromId(String id) {
            for (Position position : values()) {
                if (position.id.equals(id)) {
                    return position;
                }
            }
            throw new IllegalArgumentException("Unknown camera id: " + id);
        }
    }
    
    // 摄像头状态枚举
    public enum State {
        IDLE,           // 空闲
        INITIALIZING,   // 初始化中
        RECORDING,      // 录制中
        PAUSED,         // 暂停
        ERROR,          // 错误
        DISCONNECTED    // 断开连接
    }
    
    private final Position position;
    private State state;
    private String errorMessage;
    private long recordingStartTime;
    private long totalRecordingTime;
    private int currentSegment;
    private String currentFilePath;
    
    // 录制参数
    private int width = 1280;
    private int height = 800;
    private int bitRate = 1000000; // 1Mbps
    private int frameRate = 15;
    
    public CameraInfo(@NonNull Position position) {
        this.position = position;
        this.state = State.IDLE;
        this.recordingStartTime = 0;
        this.totalRecordingTime = 0;
        this.currentSegment = 0;
    }
    
    // Getters and Setters
    public Position getPosition() { return position; }
    public String getCameraId() { return position.getId(); }
    public String getDisplayName() { return position.getDisplayName(); }
    
    public State getState() { return state; }
    public void setState(State state) { this.state = state; }
    
    public String getErrorMessage() { return errorMessage; }
    public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    
    public long getRecordingStartTime() { return recordingStartTime; }
    public void setRecordingStartTime(long recordingStartTime) { 
        this.recordingStartTime = recordingStartTime; 
    }
    
    public long getTotalRecordingTime() { return totalRecordingTime; }
    public void setTotalRecordingTime(long totalRecordingTime) { 
        this.totalRecordingTime = totalRecordingTime; 
    }
    
    public int getCurrentSegment() { return currentSegment; }
    public void setCurrentSegment(int currentSegment) { this.currentSegment = currentSegment; }
    
    public String getCurrentFilePath() { return currentFilePath; }
    public void setCurrentFilePath(String currentFilePath) { 
        this.currentFilePath = currentFilePath; 
    }
    
    // 录制参数
    public int getWidth() { return width; }
    public void setWidth(int width) { this.width = width; }
    
    public int getHeight() { return height; }
    public void setHeight(int height) { this.height = height; }
    
    public int getBitRate() { return bitRate; }
    public void setBitRate(int bitRate) { this.bitRate = bitRate; }
    
    public int getFrameRate() { return frameRate; }
    public void setFrameRate(int frameRate) { this.frameRate = frameRate; }
    
    // 便利方法
    public boolean isRecording() {
        return state == State.RECORDING;
    }
    
    public boolean isIdle() {
        return state == State.IDLE;
    }
    
    public boolean hasError() {
        return state == State.ERROR;
    }
    
    public long getCurrentRecordingDuration() {
        if (recordingStartTime > 0 && isRecording()) {
            return System.currentTimeMillis() - recordingStartTime;
        }
        return 0;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CameraInfo that = (CameraInfo) o;
        return position == that.position;
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(position);
    }
    
    @Override
    public String toString() {
        return "CameraInfo{" +
                "position=" + position +
                ", state=" + state +
                ", recordingStartTime=" + recordingStartTime +
                ", currentSegment=" + currentSegment +
                '}';
    }
}
