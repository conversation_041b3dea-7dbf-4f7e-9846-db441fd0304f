package com.autolink.sbjk;

import android.content.pm.PackageManager;
import android.graphics.SurfaceTexture;
import android.os.Bundle;
import android.view.Surface;
import android.view.SurfaceHolder;
import android.view.SurfaceView;
import android.view.TextureView;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.ViewModelProvider;

import com.autolink.sbjk.common.constant.CameraConstants;
import com.autolink.sbjk.common.util.LogUtil;
import com.autolink.sbjk.di.DIContainer;
import com.autolink.sbjk.viewmodel.CameraPreviewViewModel;
import com.autolink.sbjk.viewmodel.CameraPreviewViewModelFactory;

/**
 * 相机预览Activity - MVVM架构重构版
 * 职责：UI展示、用户交互、状态观察
 */
public class CameraPreviewActivity extends AppCompatActivity {

    private static final String TAG = "CameraPreviewActivity";
    private static final int REQUEST_CAMERA_PERMISSION = 1;

    // UI组件
    private SurfaceView previewView;
    private Button btnStartStop;
    private TextView tvDuration;
    private TextView tvCameraInfo;

    // ViewModel
    private CameraPreviewViewModel previewViewModel;
    
    // Surface监听器 - 通过ViewModel管理
    private final SurfaceHolder.Callback surfaceHolderCallback = new SurfaceHolder.Callback() {
        @Override
        public void surfaceCreated(@NonNull SurfaceHolder holder) {
            LogUtil.d(TAG, "Surface created");
            previewViewModel.onPreviewSurfaceAvailable(holder.getSurface());
        }

        @Override
        public void surfaceChanged(@NonNull SurfaceHolder holder, int format, int width, int height) {
            LogUtil.d(TAG, "Surface changed: " + width + "x" + height + ", format: " + format);
            previewViewModel.onPreviewSurfaceChanged(holder.getSurface());
        }

        @Override
        public void surfaceDestroyed(@NonNull SurfaceHolder holder) {
            LogUtil.d(TAG, "Surface destroyed");
            if (!isChangingConfigurations()) {
                previewViewModel.onPreviewSurfaceDestroyed();
            }
        }
    };
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 保持屏幕常亮
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);

        setContentView(R.layout.activity_camera_preview);

        // 使用依赖注入初始化ViewModel
        initializeViewModel();

        // 初始化UI组件
        initViews();

        // 设置观察者
        setupObservers();

        // 检查权限
        checkPermissions();

        LogUtil.d(TAG, "CameraPreviewActivity created with MVVM architecture");
    }
    
    /**
     * 初始化ViewModel
     */
    private void initializeViewModel() {
        // 使用ViewModelFactory确保配置更改时状态保持
        CameraPreviewViewModelFactory factory = new CameraPreviewViewModelFactory(this);
        previewViewModel = new ViewModelProvider(this, factory).get(CameraPreviewViewModel.class);
        LogUtil.d(TAG, "CameraPreviewViewModel created with ViewModelProvider and custom factory");

        // 设置默认相机ID（前置摄像头）
        previewViewModel.setCurrentCameraId(CameraConstants.CAMERA_FRONT);
    }

    /**
     * 初始化UI组件
     */
    private void initViews() {
        previewView = findViewById(R.id.camera_surface_view);
        btnStartStop = findViewById(R.id.btnStartStop);

        // 可选的UI组件设置为null，因为布局中不存在
        // 这些组件是为了展示功能而设计的，但在当前布局中不是必需的
        tvDuration = null;
        tvCameraInfo = null;

        // 验证必需的UI组件
        if (previewView == null) {
            LogUtil.e(TAG, "SurfaceView not found in layout");
            finish();
            return;
        }

        if (btnStartStop == null) {
            LogUtil.e(TAG, "Start/Stop button not found in layout");
            finish();
            return;
        }

        // 设置按钮点击事件
        btnStartStop.setOnClickListener(v -> {
            if (hasPermissions()) {
                previewViewModel.toggleRecording();
            } else {
                checkPermissions();
            }
        });

        // 设置Surface监听器
        previewView.getHolder().addCallback(surfaceHolderCallback);

        LogUtil.d(TAG, "UI components initialized - Core components ready, " +
                      "optional components (Duration/CameraInfo) not available in current layout");
    }

    /**
     * 设置观察者
     */
    private void setupObservers() {
        // 观察录制状态
        previewViewModel.isRecording.observe(this, this::updateRecordingState);

        // 观察加载状态
        previewViewModel.isLoading.observe(this, isLoading ->
            btnStartStop.setEnabled(isLoading == null || !isLoading));

        // 观察相机准备状态
        previewViewModel.isCameraReady.observe(this, this::updateCameraReadyState);

        // 观察当前相机ID
        previewViewModel.currentCameraId.observe(this, this::updateCameraInfo);

        // 观察录制时长
        previewViewModel.recordingDuration.observe(this, this::updateDuration);

        // 观察错误消息
        previewViewModel.errorMessage.observe(this, this::showErrorMessage);

        // 观察成功消息
        previewViewModel.successMessage.observe(this, this::showSuccessMessage);
    }

    @Override
    protected void onResume() {
        super.onResume();

        // 如果Surface已经可用，通知ViewModel
        if (previewView.getHolder().getSurface() != null && previewView.getHolder().getSurface().isValid()) {
            previewViewModel.onPreviewSurfaceAvailable(previewView.getHolder().getSurface());
        }

        LogUtil.d(TAG, "Activity resumed");
    }
    
    @Override
    protected void onPause() {
        super.onPause();
        LogUtil.d(TAG, "Activity paused");
    }

    /**
     * 状态更新方法
     */
    private void updateRecordingState(Boolean isRecording) {
        if (isRecording != null && btnStartStop != null) {
            btnStartStop.setText(isRecording ? "停止录制" : "开始录制");
            btnStartStop.setActivated(isRecording);
            LogUtil.d(TAG, "Recording state updated: " + isRecording);
        }
    }

    private void updateCameraReadyState(Boolean isReady) {
        if (isReady != null) {
            btnStartStop.setEnabled(isReady);
            LogUtil.d(TAG, "Camera ready state: " + isReady);
        }
    }

    private void updateCameraInfo(String cameraId) {
        if (cameraId != null && tvCameraInfo != null) {
            String cameraName = CameraConstants.getCameraName(cameraId);
            tvCameraInfo.setText("相机: " + cameraName);
            LogUtil.d(TAG, "Camera info updated: " + cameraName);
        } else if (cameraId != null) {
            // 相机信息通过日志记录，即使UI组件不可用也能追踪状态
            String cameraName = CameraConstants.getCameraName(cameraId);
            LogUtil.d(TAG, "Camera info (log only): " + cameraName);
        }
    }

    private void updateDuration(String duration) {
        if (duration != null && tvDuration != null) {
            tvDuration.setText(duration);
        } else if (duration != null) {
            // 录制时长通过日志记录，便于调试和监控
            LogUtil.d(TAG, "Recording duration (log only): " + duration);
        }
    }

    private void showErrorMessage(String error) {
        if (error != null && !error.isEmpty()) {
            Toast.makeText(this, error, Toast.LENGTH_LONG).show();
            previewViewModel.clearError();
        }
    }

    private void showSuccessMessage(String success) {
        if (success != null && !success.isEmpty()) {
            Toast.makeText(this, success, Toast.LENGTH_SHORT).show();
            previewViewModel.clearSuccess();
        }
    }

    /**
     * 权限管理
     */
    private void checkPermissions() {
        if (!hasPermissions()) {
            ActivityCompat.requestPermissions(this, CameraConstants.REQUIRED_PERMISSIONS, REQUEST_CAMERA_PERMISSION);
        }
    }

    private boolean hasPermissions() {
        for (String permission : CameraConstants.REQUIRED_PERMISSIONS) {
            if (ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED) {
                return false;
            }
        }
        return true;
    }
    
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == REQUEST_CAMERA_PERMISSION) {
            boolean allGranted = true;
            for (int result : grantResults) {
                if (result != PackageManager.PERMISSION_GRANTED) {
                    allGranted = false;
                    break;
                }
            }

            if (allGranted) {
                LogUtil.d(TAG, "All permissions granted");
                // 如果Surface已经可用，通知ViewModel
                if (previewView.getHolder().getSurface() != null && previewView.getHolder().getSurface().isValid()) {
                    previewViewModel.onPreviewSurfaceAvailable(previewView.getHolder().getSurface());
                }
            } else {
                Toast.makeText(this, "需要相机和存储权限才能使用此功能", Toast.LENGTH_SHORT).show();
                finish();
            }
        }
    }
}