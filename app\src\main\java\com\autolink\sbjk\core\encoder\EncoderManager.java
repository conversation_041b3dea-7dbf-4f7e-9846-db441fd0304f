package com.autolink.sbjk.core.encoder;

import android.content.Context;

import com.autolink.sbjk.common.constant.CameraConstants;
import com.autolink.sbjk.common.util.LogUtil;
import com.autolink.sbjk.encoder.CameraYuvEncoder;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.Map;

/**
 * 编码器管理器
 * 统一管理所有相机的视频编码器实例
 */
public class EncoderManager {
    
    private static final String TAG = "EncoderManager";
    private static volatile EncoderManager instance;
    
    private final Context context;
    private final Map<String, CameraYuvEncoder> encoderMap;
    private final ExecutorService executorService;
    private final Object lock = new Object();
    
    private EncoderManager(Context context) {
        this.context = context.getApplicationContext();
        this.encoderMap = new ConcurrentHashMap<>();
        this.executorService = Executors.newCachedThreadPool();
        
        LogUtil.d(TAG, "EncoderManager initialized");
    }
    
    public static EncoderManager getInstance(Context context) {
        if (instance == null) {
            synchronized (EncoderManager.class) {
                if (instance == null) {
                    instance = new EncoderManager(context);
                }
            }
        }
        return instance;
    }
    
    /**
     * 获取或创建编码器实例
     */
    public CameraYuvEncoder getOrCreateEncoder(String cameraId) {
        if (!CameraConstants.isValidCameraId(cameraId)) {
            LogUtil.e(TAG, "Invalid camera ID: " + cameraId);
            return null;
        }
        
        CameraYuvEncoder encoder = encoderMap.get(cameraId);
        if (encoder == null) {
            synchronized (lock) {
                encoder = encoderMap.get(cameraId);
                if (encoder == null) {
                    encoder = createEncoder(cameraId);
                    if (encoder != null) {
                        encoderMap.put(cameraId, encoder);
                        LogUtil.d(TAG, "Created new encoder for camera " + cameraId);
                    }
                }
            }
        }
        return encoder;
    }
    
    /**
     * 创建编码器实例
     */
    private CameraYuvEncoder createEncoder(String cameraId) {
        try {
            CameraYuvEncoder encoder = new CameraYuvEncoder(context);
            
            // 配置编码器参数
            configureEncoder(encoder, cameraId);
            
            LogUtil.d(TAG, "Encoder created and configured for camera " + cameraId);
            return encoder;
            
        } catch (Exception e) {
            LogUtil.e(TAG, "Failed to create encoder for camera " + cameraId, e);
            return null;
        }
    }
    
    /**
     * 配置编码器参数
     */
    private void configureEncoder(CameraYuvEncoder encoder, String cameraId) {
        try {
            // 设置基础参数
            encoder.setBitRate(CameraConstants.DEFAULT_BIT_RATE);
            encoder.setSegmentRecording(true, CameraConstants.DEFAULT_SEGMENT_DURATION_MINUTES);
            
            // 根据相机ID设置特定参数
            switch (cameraId) {
                case CameraConstants.CAMERA_FRONT:
                case CameraConstants.CAMERA_BACK:
                    // 前后摄像头使用较高质量
                    encoder.setBitRate(CameraConstants.HIGH_QUALITY_BIT_RATE);
                    break;
                case CameraConstants.CAMERA_LEFT:
                case CameraConstants.CAMERA_RIGHT:
                    // 左右摄像头使用标准质量
                    encoder.setBitRate(CameraConstants.STANDARD_BIT_RATE);
                    break;
                default:
                    LogUtil.w(TAG, "Unknown camera ID, using default settings: " + cameraId);
                    break;
            }
            
            LogUtil.d(TAG, "Encoder configured for camera " + cameraId);
            
        } catch (Exception e) {
            LogUtil.e(TAG, "Failed to configure encoder for camera " + cameraId, e);
        }
    }
    
    /**
     * 启动编码器
     */
    public boolean startEncoder(String cameraId, String outputPath) {
        CameraYuvEncoder encoder = getOrCreateEncoder(cameraId);
        if (encoder == null) {
            LogUtil.e(TAG, "Cannot start encoder: encoder not found for camera " + cameraId);
            return false;
        }

        try {
            // 设置输出路径
            encoder.setRecordPath(outputPath);

            // 启动编码器 (startTest方法返回void)
            encoder.startTest(cameraId);
            LogUtil.i(TAG, "Encoder started for camera " + cameraId);
            return true;

        } catch (Exception e) {
            LogUtil.e(TAG, "Exception starting encoder for camera " + cameraId, e);
            return false;
        }
    }
    
    /**
     * 停止编码器
     */
    public boolean stopEncoder(String cameraId) {
        CameraYuvEncoder encoder = encoderMap.get(cameraId);
        if (encoder == null) {
            LogUtil.w(TAG, "Cannot stop encoder: encoder not found for camera " + cameraId);
            return true; // 已经停止了，返回true
        }
        
        try {
            encoder.stopTest();
            LogUtil.i(TAG, "Encoder stopped for camera " + cameraId);
            return true;
            
        } catch (Exception e) {
            LogUtil.e(TAG, "Exception stopping encoder for camera " + cameraId, e);
            return false;
        }
    }
    
    /**
     * 移除编码器实例
     */
    public void removeEncoder(String cameraId) {
        synchronized (lock) {
            CameraYuvEncoder encoder = encoderMap.remove(cameraId);
            if (encoder != null) {
                try {
                    encoder.stopTest();
                    LogUtil.d(TAG, "Encoder removed for camera " + cameraId);
                } catch (Exception e) {
                    LogUtil.e(TAG, "Exception removing encoder for camera " + cameraId, e);
                }
            }
        }
    }
    
    /**
     * 获取编码器状态
     */
    public boolean isEncoderActive(String cameraId) {
        CameraYuvEncoder encoder = encoderMap.get(cameraId);
        return encoder != null; // 简化的状态检查
    }
    
    /**
     * 获取活跃编码器数量
     */
    public int getActiveEncoderCount() {
        return encoderMap.size();
    }
    
    /**
     * 停止所有编码器
     */
    public void stopAllEncoders() {
        LogUtil.i(TAG, "Stopping all encoders");
        
        for (String cameraId : encoderMap.keySet()) {
            stopEncoder(cameraId);
        }
    }
    
    /**
     * 释放所有资源
     */
    public void release() {
        LogUtil.i(TAG, "Releasing EncoderManager resources");
        
        stopAllEncoders();
        
        synchronized (lock) {
            encoderMap.clear();
        }
        
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
        
        LogUtil.d(TAG, "EncoderManager resources released");
    }
    
    /**
     * 获取编码器统计信息
     */
    public String getEncoderStats() {
        return "EncoderManager Stats: " +
               "Active encoders=" + encoderMap.size() +
               ", Cameras=" + encoderMap.keySet().toString();
    }
}
