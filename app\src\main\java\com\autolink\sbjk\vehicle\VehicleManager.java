package com.autolink.sbjk.vehicle;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.IBinder;
import android.os.RemoteException;
import android.widget.Toast;

import com.autolink.app.vehicleservice.IVehicleControl;
import com.autolink.app.vehicleservice.IVehicleControlCallback;
import com.autolink.sbjk.common.util.LogUtil;
import com.autolink.sbjk.vehicle.VehiclePropertyKey;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 车辆服务管理器，负责与VehicleService通信
 */
public class VehicleManager {
    private static final String TAG = "VehicleManager";
    
    // 钥匙状态枚举
    public static final int KEY_STATE_OFF = 0;    // 关闭
    public static final int KEY_STATE_ACC = 1;    // 配件
    public static final int KEY_STATE_ON = 2;     // 打开
    public static final int KEY_STATE_CRANK = 3;  // 启动
    
    // 档位状态枚举
    public static final int GEAR_POSITION_R = 2;  // R档
    public static final int GEAR_POSITION_P = 1;  // P档
    public static final int GEAR_POSITION_N = 3;  // N档 
    public static final int GEAR_POSITION_D = 4;  // D档
    public static final int GEAR_POSITION_S = 50;  // S档
    public static final int GEAR_POSITION_L = 60;  // L档
    
    private static VehicleManager sInstance;
    private final Context mContext;
    private IVehicleControl mVehicleControl;
    private boolean mIsBound = false;
    private final List<VehicleDataListener> mListeners = new CopyOnWriteArrayList<>();
    
    // 单例模式
    public static synchronized VehicleManager getInstance(Context context) {
        if (sInstance == null) {
            sInstance = new VehicleManager(context.getApplicationContext());
        }
        return sInstance;
    }
    
    private VehicleManager(Context context) {
        mContext = context;
        LogUtil.i(TAG, "VehicleManager初始化，准备绑定服务");
        bindVehicleService();
    }
    
    // 车辆数据监听器接口
    public interface VehicleDataListener {
        void onVehiclePropertyChanged(VehiclePropertyKey key, Object value, boolean selfGet);
    }
    
    // 添加监听器
    public void addListener(VehicleDataListener listener) {
        if (listener != null && !mListeners.contains(listener)) {
            mListeners.add(listener);
            LogUtil.i(TAG, "添加车辆数据监听器: " + listener.getClass().getSimpleName());
        }
    }
    
    // 移除监听器
    public void removeListener(VehicleDataListener listener) {
        if (listener != null) {
            mListeners.remove(listener);
            LogUtil.i(TAG, "移除车辆数据监听器: " + listener.getClass().getSimpleName());
        }
    }
    
    // 绑定车辆服务
    private void bindVehicleService() {
        if (mIsBound) {
            LogUtil.i(TAG, "车辆服务已绑定，无需重复绑定");
            return;
        }
        
        try {
            Intent intent = new Intent();
            intent.setComponent(new ComponentName(
                    "com.autolink.app.vehicleservice",
                    "com.autolink.app.vehicleservice.VehicleService"));
            
            mIsBound = mContext.bindService(intent, mServiceConnection, Context.BIND_AUTO_CREATE);
            LogUtil.i(TAG, "绑定车辆服务: " + (mIsBound ? "成功" : "失败"));
            
            if (!mIsBound) {
                Toast.makeText(mContext, "车辆服务绑定失败，请检查服务是否运行", Toast.LENGTH_LONG).show();
            }
        } catch (Exception e) {
            LogUtil.e(TAG, "绑定车辆服务异常", e);
            Toast.makeText(mContext, "绑定车辆服务异常: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }
    
    // 解绑车辆服务
    public void unbindVehicleService() {
        if (mIsBound) {
            try {
                if (mVehicleControl != null && mCallback != null) {
                    mVehicleControl.unregisterCallback(mCallback);
                    LogUtil.i(TAG, "已注销车辆属性回调");
                }
            } catch (RemoteException e) {
                LogUtil.e(TAG, "解注册回调失败", e);
            }
            
            mContext.unbindService(mServiceConnection);
            mVehicleControl = null;
            mIsBound = false;
            LogUtil.i(TAG, "解绑车辆服务");
        }
    }
    
    // 手动重新绑定服务
    public void rebindVehicleService() {
        LogUtil.i(TAG, "手动重新绑定车辆服务");
        if (mIsBound) {
            unbindVehicleService();
        }
        bindVehicleService();
    }
    
    // 服务连接回调
    private final ServiceConnection mServiceConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            LogUtil.i(TAG, "车辆服务已连接: " + name.flattenToShortString());
            try {
                mVehicleControl = IVehicleControl.Stub.asInterface(service);
                if (mVehicleControl != null) {
                    registerVehicleCallbacks();
                } else {
                    LogUtil.e(TAG, "IVehicleControl接口为null");
                }
            } catch (Exception e) {
                LogUtil.e(TAG, "初始化车辆控制接口异常", e);
                Toast.makeText(mContext, "初始化车辆控制接口异常: " + e.getMessage(), Toast.LENGTH_LONG).show();
            }
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {
            LogUtil.w(TAG, "车辆服务已断开: " + name.flattenToShortString());
            mVehicleControl = null;
            mIsBound = false;
            
            // 尝试重新绑定
            new Thread(() -> {
                try {
                    LogUtil.i(TAG, "等待500毫秒后尝试重新绑定车辆服务");
                    Thread.sleep(500);
                    bindVehicleService();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }).start();
        }
    };

    // 车辆服务回调实现
    private final IVehicleControlCallback mCallback = new IVehicleControlCallback.Stub() {
        @Override
        public void onReceiveInt(int propKey, int value, boolean selfGet) throws RemoteException {
            VehiclePropertyKey key = VehiclePropertyKey.valueOf(propKey);
            if (key != null) {
                // 只处理钥匙状态和档位状态
                if (key == VehiclePropertyKey.KeyVehiclePropertyKeySts) {
                    LogUtil.i(TAG, "钥匙状态变化: " + getKeyStateName(value));
                    notifyListeners(key, value, selfGet);
                } else if (key == VehiclePropertyKey.KeyVehiclePropertyGearPosition) {
                    LogUtil.i(TAG, "档位状态变化: " + getGearPositionName(value));
                    notifyListeners(key, value, selfGet);
                }
            }
        }

        @Override
        public void onReceiveFloat(int propKey, float value, boolean selfGet) throws RemoteException {
            // 钥匙状态和档位状态都是整型，不处理浮点型
        }

        @Override
        public void onOnlySyncIntData(int propKey, int value) throws RemoteException {
            VehiclePropertyKey key = VehiclePropertyKey.valueOf(propKey);
            if (key != null) {
                // 只处理钥匙状态和档位状态的同步数据
                if (key == VehiclePropertyKey.KeyVehiclePropertyKeySts ||
                    key == VehiclePropertyKey.KeyVehiclePropertyGearPosition) {
                    notifyListeners(key, value, false);
                }
            }
        }

        @Override
        public void onOnlySyncFloatData(int propKey, float value) throws RemoteException {
            // 钥匙状态和档位状态都是整型，不处理浮点型
        }
    };

    // 通知所有监听器
    private void notifyListeners(VehiclePropertyKey key, Object value, boolean selfGet) {
        if (mListeners.isEmpty()) {
            LogUtil.i(TAG, "没有注册的监听器，不通知车辆数据变化");
            return;
        }

        for (VehicleDataListener listener : mListeners) {
            try {
                listener.onVehiclePropertyChanged(key, value, selfGet);
            } catch (Exception e) {
                LogUtil.e(TAG, "通知监听器失败: " + listener.getClass().getSimpleName(), e);
            }
        }
    }

    // 注册车辆回调
    private void registerVehicleCallbacks() {
        if (mVehicleControl == null) {
            LogUtil.e(TAG, "车辆服务未连接，无法注册回调");
            return;
        }

        try {
            // 只监听钥匙状态和档位状态
            int[] propertyKeys = new int[] {
                VehiclePropertyKey.KeyVehiclePropertyKeySts.getValue(),      // 钥匙状态
                VehiclePropertyKey.KeyVehiclePropertyGearPosition.getValue(), // 档位状态
            };

            LogUtil.i(TAG, "注册车辆属性回调，属性数量: " + propertyKeys.length);

            // 注册回调
            mVehicleControl.registerCallback(mCallback, propertyKeys);
            LogUtil.i(TAG, "已注册车辆属性回调");



        } catch (RemoteException e) {
            LogUtil.e(TAG, "注册车辆属性回调失败", e);
            Toast.makeText(mContext, "注册车辆属性回调失败: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    // 获取整型属性（只支持钥匙状态和档位状态）
    public int getIntProperty(VehiclePropertyKey key) {
        if (!isConnected()) {
            LogUtil.e(TAG, "车辆服务未连接，无法获取属性: " + (key != null ? key.getDesc() : "null"));
            return -1;
        }

        if (key == null) {
            LogUtil.e(TAG, "属性键为空");
            return -1;
        }

        // 只支持钥匙状态和档位状态
        if (key != VehiclePropertyKey.KeyVehiclePropertyKeySts &&
            key != VehiclePropertyKey.KeyVehiclePropertyGearPosition) {
            LogUtil.w(TAG, "不支持的属性: " + key.getDesc());
            return -1;
        }

        try {
            int value = mVehicleControl.getInt(key.getValue());
            if (key == VehiclePropertyKey.KeyVehiclePropertyKeySts) {
                LogUtil.d(TAG, "获取钥匙状态: " + getKeyStateName(value));
            } else if (key == VehiclePropertyKey.KeyVehiclePropertyGearPosition) {
                LogUtil.d(TAG, "获取档位状态: " + getGearPositionName(value));
            }
            return value;
        } catch (RemoteException e) {
            LogUtil.e(TAG, "获取属性失败: " + key.getDesc(), e);
            return -1;
        }
    }



    // 设置整型属性（只支持钥匙状态和档位状态，但通常不需要设置）
    public boolean setIntProperty(VehiclePropertyKey key, int value) {
        if (!isConnected()) {
            LogUtil.e(TAG, "车辆服务未连接，无法设置属性");
            return false;
        }

        if (key == null) {
            LogUtil.e(TAG, "属性键为空");
            return false;
        }

        // 只支持钥匙状态和档位状态（但通常只读取，不设置）
        if (key != VehiclePropertyKey.KeyVehiclePropertyKeySts &&
            key != VehiclePropertyKey.KeyVehiclePropertyGearPosition) {
            LogUtil.w(TAG, "不支持设置的属性: " + key.getDesc());
            return false;
        }

        try {
            mVehicleControl.setInt(key.getValue(), value);
            LogUtil.i(TAG, "设置属性: " + key.getDesc() + " = " + value);
            return true;
        } catch (RemoteException e) {
            LogUtil.e(TAG, "设置属性失败: " + key.getDesc(), e);
            return false;
        }
    }



    // 获取当前档位信息
    public String getGearPositionName(int gearPosition) {
        switch (gearPosition) {
            case GEAR_POSITION_P:
                return "P档";
            case GEAR_POSITION_R:
                return "R档";
            case GEAR_POSITION_N:
                return "N档";
            case GEAR_POSITION_D:
                return "D档";
            case GEAR_POSITION_S:
                return "S档";
            case GEAR_POSITION_L:
                return "L档";
            default:
                return "未知档位(" + gearPosition + ")";
        }
    }

    // 获取钥匙状态名称
    public String getKeyStateName(int keyState) {
        switch (keyState) {
            case KEY_STATE_OFF:
                return "钥匙OFF";
            case KEY_STATE_ACC:
                return "钥匙ACC";
            case KEY_STATE_ON:
                return "钥匙ON";
            case KEY_STATE_CRANK:
                return "钥匙CRANK";
            default:
                return "未知状态(" + keyState + ")";
        }
    }

    // 获取发动机状态名称
    public String getEngineStateName(int engineState) {
        return engineState == 1 ? "发动机运行中" : "发动机停止";
    }

    // 检查是否已连接
    public boolean isConnected() {
        return mIsBound && mVehicleControl != null;
    }
}
