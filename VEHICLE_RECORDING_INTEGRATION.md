# 车辆状态录制控制集成文档

## 功能概述

本项目已成功集成车辆状态监控功能，实现了基于车辆档位和钥匙状态的智能录制控制。

## 核心功能

### 1. 手动启动录制
- **触发方式**: 用户点击录制按钮
- **逻辑**: 检查车辆状态 → 满足条件则启动录制，不满足则提示用户
- **条件**: 钥匙状态 >= ACC(1) 且 档位 = P档(1)
- **提示**: 不满足条件时显示"请先启动车辆"或"请挂入P档后启动"

### 2. 哨兵自动启动
- **触发方式**: 用户开启"自动启动哨兵功能"开关
- **逻辑**: 监听车辆状态变化 → 条件满足时自动启动录制
- **条件**: 与手动启动相同
- **提示**: 自动启动时显示"哨兵模式自动启动录制"

### 3. 自动停止录制
- **触发方式**: 车辆状态变化（用户无法操控）
- **逻辑**: 检测到条件不满足 → 立即停止录制
- **条件**: 钥匙状态 < ACC(1) 或 档位 != P档(1)
- **提示**: 自动停止时显示"条件不满足，自动停止录制"

## 技术架构

### 核心组件

1. **VehicleManager**: 车辆服务通信管理器
   - 负责与车辆ECU服务的AIDL通信
   - 专门监听钥匙状态和档位状态变化
   - 提供简化的属性读取接口

2. **VehicleRecordingController**: 智能录制控制器
   - 实现车辆状态与录制系统的桥梁
   - 处理条件检查和录制控制逻辑
   - 管理手动/自动录制状态

3. **VehiclePropertyKey**: 车辆属性定义
   - 只定义钥匙状态和档位状态属性
   - 提供简化的属性映射和查找功能

### 集成方式

- **非侵入式设计**: 不修改现有MainViewModel和录制逻辑
- **依赖注入**: 通过DIContainer管理组件依赖
- **服务层集成**: 在CameraService中初始化车辆控制器
- **UI层扩展**: 在MainActivity中添加哨兵模式开关

## 使用方法

### 开发者使用

1. **获取车辆控制器实例**:
```java
VehicleRecordingController controller = DIContainer.provideVehicleRecordingController(context);
controller.initialize();
```

2. **手动启动录制**:
```java
controller.startManualRecording(new VehicleRecordingController.ConditionCallback() {
    @Override
    public void onConditionMet() {
        // 录制已启动
    }
    
    @Override
    public void onConditionNotMet(String reason) {
        // 显示提示信息
    }
});
```

3. **设置哨兵自动模式**:
```java
controller.setSentryAutoMode(true); // 开启自动模式
```

### 用户使用

1. **手动录制**: 仅在哨兵模式关闭时可用，点击录制按钮，系统会检查车辆状态
2. **哨兵模式**: 在哨兵监控页面开启"自动启动哨兵功能"开关，手动录制按钮将被隐藏
3. **模式切换**: 开启哨兵模式时隐藏手动按钮，关闭哨兵模式时显示手动按钮
4. **自动停止**: 系统会根据车辆状态自动停止录制

## 录制条件配置

### 当前配置
- **开始录制**: 钥匙状态 >= ACC(1) 且 档位 = P档(1)
- **停止录制**: 钥匙状态 < ACC(1) 或 档位 != P档(1)

### 状态值定义
```java
// 钥匙状态
KEY_STATE_OFF = 0    // 关闭
KEY_STATE_ACC = 1    // 配件
KEY_STATE_ON = 2     // 打开
KEY_STATE_CRANK = 3  // 启动

// 档位状态
GEAR_POSITION_P = 1  // P档
GEAR_POSITION_R = 2  // R档
GEAR_POSITION_N = 3  // N档
GEAR_POSITION_D = 4  // D档
GEAR_POSITION_S = 50 // S档
GEAR_POSITION_L = 60 // L档
```

## 错误处理

### 车辆服务连接失败
- 系统会降级到普通录制模式
- 不影响基本录制功能
- 日志记录连接失败信息

### 属性获取异常
- 返回默认值(-1)
- 记录异常日志
- 继续尝试重新连接

## 日志和调试

### 关键日志标签
- `VehicleManager`: 车辆服务通信
- `VehicleRecordingController`: 录制控制逻辑
- `CameraService`: 服务集成
- `MainActivity`: UI交互

### 调试信息
- 车辆状态变化日志
- 录制条件检查结果
- 自动启动/停止事件
- 服务连接状态

## 测试建议

1. **模拟车辆状态变化**: 测试不同钥匙和档位组合
2. **网络断线测试**: 验证车辆服务断开时的降级处理
3. **长时间运行测试**: 确保内存泄漏和稳定性
4. **用户交互测试**: 验证手动和自动模式的切换

## 扩展性

### 添加新的录制条件
1. 在`VehicleRecordingController.isRecordingConditionMet()`中修改逻辑
2. 在`VehiclePropertyKey`中添加新的属性定义
3. 在`VehicleManager.registerVehicleCallbacks()`中注册新属性

### 自定义提示信息
1. 修改`VehicleRecordingController.getConditionFailureReason()`方法
2. 添加多语言支持
3. 自定义Toast样式

## 注意事项

1. **权限要求**: 需要车辆服务绑定权限
2. **服务依赖**: 依赖外部车辆服务运行
3. **性能影响**: 车辆状态监听会增加少量CPU使用
4. **兼容性**: 需要支持AIDL的车辆系统

## 版本信息

- **集成版本**: v1.0
- **支持的车辆属性**: 钥匙状态、档位状态、车速、发动机状态
- **兼容的Android版本**: API 30+
- **测试状态**: 开发完成，待测试验证
