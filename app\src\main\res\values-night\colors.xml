<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 日夜模式适配颜色 - 夜间模式 -->
    <!-- 主要文本颜色 - 使用位置：ThemeManager.java、ThemeApplier.java、通过text_adaptive在activity_main.xml和item_video_record.xml -->
    <color name="text_primary_adaptive">#FFFFFF</color>
    <!-- 次要文本颜色 - 使用位置：ThemeManager.java、ThemeApplier.java、item_video_record.xml录制时间 -->
    <color name="text_secondary_adaptive">#CCCCCC</color>
    <!-- 主要背景颜色 - 使用位置：ThemeManager.java、ThemeApplier.java、MainActivity.java各页面背景 -->
    <color name="background_primary_adaptive">#202020</color>
    <!-- 次要背景颜色 - 使用位置：ThemeManager.java -->
    <color name="background_secondary_adaptive">#202020</color>
    <!-- 容器背景颜色 - 使用位置：ThemeManager.java、ThemeApplier.java、MainActivity.java相机容器背景 -->
    <color name="container_background_adaptive">#1A1A1A</color>
    <!-- 选中按钮文字颜色 - 使用位置：ThemeManager.java、ThemeApplier.java、MainActivity.java页面按钮 -->
    <color name="button_text_selected_adaptive">#FFFFFF</color>
    <!-- 未选中按钮文字颜色 - 使用位置：ThemeManager.java、ThemeApplier.java、activity_main.xml筛选按钮 -->
    <color name="button_text_unselected_adaptive">#808080</color>
    <!-- 背景颜色 -->
    <color name="background_light">#323232</color>
    <!-- 系统UI颜色 - 夜间模式 -->
    <!-- 状态栏颜色 - 使用位置：themes.xml、ThemeManager.java、MainActivity.setupStatusBar() -->
    <color name="status_bar_color">#202020</color>
    <!-- 按钮相关颜色 - 夜间模式 -->
    <!-- 选中按钮背景颜色 - 使用位置：button_background.xml -->
    <color name="button_background_selected">#44676767</color>
    <!-- 按钮边框颜色 - 使用位置：button_outline.xml -->
    <color name="button_outline_color">#FFFFFF</color>

    <!-- 时间选择器对话框专用颜色 - 夜间模式 -->
    <!-- NumberPicker文字颜色 - 使用位置：TimePickerManager.java中NumberPicker的数字显示 -->
    <color name="number_picker_text_color">#000000</color>
    <!-- 分隔线颜色 - 使用位置：TimePickerManager.java中月/日/时选择器之间的分隔线 -->
    <color name="separator_line_color">#D8D8D8</color>
    <!-- 对话框标签文字颜色 - 使用位置：TimePickerManager.java中"月份"、"日期"、"小时"标签 -->
    <color name="dialog_label_text_color">#D8D8D8</color>
    <!-- 对话框背景颜色 - 使用位置：TimePickerManager.java中时间选择对话框背景 -->
    <color name="dialog_background_color">#858585</color>

    <!-- 播放器控制栏专用颜色 - 夜间模式（与日间模式相同，因为有固定的半透明背景） -->
    <!-- 播放器控制栏文字颜色 - 使用位置：MainActivity.java播放器控制栏按钮和文字，固定白色因为有半透明黑色背景 -->
    <color name="player_control_text_color">#FFFFFF</color>


    <!-- 兼容性保留颜色 - 夜间模式，为了保持向后兼容性 -->
    <!-- 适配文本颜色 - 使用位置：activity_main.xml筛选按钮、item_video_record.xml摄像头方向文字 -->
    <color name="text_adaptive">@color/text_primary_adaptive</color>

    <!-- 未选中按钮文字颜色 - 使用位置：activity_main.xml摄像头筛选按钮、时间筛选按钮 -->
    <color name="button_text_unselected">@color/button_text_unselected_adaptive</color>
</resources>
