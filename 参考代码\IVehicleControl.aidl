package com.autolink.app.vehicleservice;

import com.autolink.app.vehicleservice.IVehicleControlCallback;

interface IVehicleControl {
    void registerCallback(IVehicleControlCallback callback, in int[] propertyKeys);
    void unregisterCallback(IVehicleControlCallback callback);
    
    void setInt(int propKey, int value);
    void setFloat(int propKey, float value);
    int getInt(int propKey);
    float getFloat(int propKey);
} 