package com.autolink.sbjk.adapter;

import android.content.res.Configuration;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.DiffUtil;
import androidx.recyclerview.widget.ListAdapter;
import androidx.recyclerview.widget.RecyclerView;

import com.autolink.sbjk.R;
import com.autolink.sbjk.model.VideoRecordInfo;
import com.autolink.sbjk.common.theme.ThemeManager;
import com.autolink.sbjk.common.theme.ThemeApplier;
import com.autolink.sbjk.common.util.LogUtil;

/**
 * 录像列表适配器
 * 使用ListAdapter实现高效的列表更新
 *
 * 功能说明：
 * - 实现ThemeManager.ThemeChangeListener接口，统一处理主题变化
 * - 使用缓存的主题颜色，避免重复主题检测
 * - 使用ThemeApplier统一应用列表项主题
 * - 优化列表滚动性能
 */
public class VideoListAdapter extends ListAdapter<VideoRecordInfo, VideoListAdapter.VideoViewHolder>
    implements ThemeManager.ThemeChangeListener {

    private static final String TAG = "VideoListAdapter";
    
    // 点击事件监听器
    public interface OnVideoClickListener {
        void onVideoClick(VideoRecordInfo video);
    }

    private OnVideoClickListener clickListener;

    // 主题管理相关
    private ThemeManager.ThemeColors currentColors;

    public VideoListAdapter() {
        super(new VideoDiffCallback());

        // 注册主题变化监听器
        ThemeManager.getInstance().addThemeChangeListener(this);
        currentColors = ThemeManager.getInstance().getCurrentColors();
        LogUtil.d(TAG, "VideoListAdapter created and theme listener registered");
    }

    // ===== 主题管理接口实现 =====

    /**
     * 主题变化回调 - ThemeManager.ThemeChangeListener接口实现
     *
     * 替换原有每个列表项的重复主题检测，统一在此处理主题变化
     *
     * @param isDarkMode 是否为深色模式
     * @param colors 当前主题颜色配置
     */
    @Override
    public void onThemeChanged(boolean isDarkMode, ThemeManager.ThemeColors colors) {
        currentColors = colors;
        LogUtil.d(TAG, "Theme changed to: " + (isDarkMode ? "Dark" : "Light") + " mode");

        // 通知所有可见的列表项更新主题
        notifyDataSetChanged();
    }

    /**
     * 应用主题（外部调用接口，兼容原有调用方式）
     *
     * @param colors 主题颜色配置
     */
    public void applyTheme(ThemeManager.ThemeColors colors) {
        currentColors = colors;
        notifyDataSetChanged();
        LogUtil.d(TAG, "Theme applied externally");
    }
    
    /**
     * 设置点击监听器
     */
    public void setOnVideoClickListener(OnVideoClickListener listener) {
        this.clickListener = listener;
    }
    
    @NonNull
    @Override
    public VideoViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_video_record, parent, false);
        return new VideoViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull VideoViewHolder holder, int position) {
        VideoRecordInfo video = getItem(position);
        // 【重构】传递缓存的主题颜色给ViewHolder，避免重复主题检测
        holder.bind(video, clickListener, currentColors);
    }
    
    /**
     * ViewHolder类
     */
    static class VideoViewHolder extends RecyclerView.ViewHolder {
        
        private final TextView cameraText;
        private final TextView timeText;
        private final View itemView;
        
        public VideoViewHolder(@NonNull View itemView) {
            super(itemView);
            this.itemView = itemView;
            this.cameraText = itemView.findViewById(R.id.tv_camera_direction);
            this.timeText = itemView.findViewById(R.id.tv_record_time);
        }
        
        /**
         * 【重构】绑定数据和主题
         *
         * 移除重复的主题检测逻辑，直接使用传入的主题颜色
         * 使用ThemeApplier统一应用主题
         */
        public void bind(VideoRecordInfo video, OnVideoClickListener clickListener, ThemeManager.ThemeColors colors) {
            if (video != null) {
                // 设置摄像头方向
                cameraText.setText(video.getCameraDirection());

                // 设置录制时间
                timeText.setText(video.getDisplayTime());

                // 【重构】直接使用传入的颜色，无需重新检测主题
                applyCurrentTheme(colors);

                // 设置点击事件
                itemView.setOnClickListener(v -> {
                    if (clickListener != null) {
                        clickListener.onVideoClick(video);
                    }
                });

                // 设置选中状态的视觉效果（可选）
                itemView.setSelected(false);
            }
        }

        /**
         * 【重构】应用当前主题
         *
         * 替换原updateColors()方法，使用ThemeApplier统一应用主题
         * 移除重复的主题检测和硬编码颜色值
         */
        private void applyCurrentTheme(ThemeManager.ThemeColors colors) {
            if (colors != null) {
                // 使用ThemeApplier应用主题，替换硬编码颜色
                ThemeApplier.applyToTextView(cameraText, colors, true);      // 主要文本
                ThemeApplier.applyToTextView(timeText, colors, false);       // 次要文本
                ThemeApplier.applyToBackground(itemView, colors, true);      // 背景
            }
        }


    }

    /**
     * 清理资源
     *
     * 在VideoListAdapter不再使用时调用，移除主题监听器防止内存泄漏
     */
    public void cleanup() {
        ThemeManager.getInstance().removeThemeChangeListener(this);
        LogUtil.d(TAG, "VideoListAdapter cleaned up, theme listener removed");
    }

    /**
     * DiffUtil回调，用于高效更新列表
     */
    private static class VideoDiffCallback extends DiffUtil.ItemCallback<VideoRecordInfo> {
        
        @Override
        public boolean areItemsTheSame(@NonNull VideoRecordInfo oldItem, @NonNull VideoRecordInfo newItem) {
            // 使用文件路径作为唯一标识
            return oldItem.getFilePath().equals(newItem.getFilePath());
        }
        
        @Override
        public boolean areContentsTheSame(@NonNull VideoRecordInfo oldItem, @NonNull VideoRecordInfo newItem) {
            // 比较所有显示相关的内容
            return oldItem.getCameraDirection().equals(newItem.getCameraDirection()) &&
                   oldItem.getDisplayTime().equals(newItem.getDisplayTime()) &&
                   oldItem.getTimestamp() == newItem.getTimestamp() &&
                   oldItem.getFileSize() == newItem.getFileSize();
        }
    }
}
