# 项目颜色资源使用详细备注

## 📋 概述

本文档详细记录了项目中所有颜色资源的定义位置和使用场景，包括日夜模式适配颜色、系统UI颜色、按钮颜色等。

## 🎨 颜色资源分类

### 1. 基础颜色定义

#### 1.1 应用主色调
**定义位置**: `app/src/main/res/values/colors.xml`

| 颜色名称 | 颜色值 | 使用位置 | 用途说明 |
|---------|--------|----------|----------|
| `colorPrimary` | `#3F51B5` | themes.xml, button_outline.xml | 应用主色调，用于主题定义和按钮边框 |
| `colorPrimaryDark` | `#303F9F` | themes.xml, navigation_bar_color | 深色主色调，用于导航栏颜色 |
| `colorAccent` | `#FF4081` | themes.xml | 强调色，用于主题定义 |

#### 1.2 基础颜色
| 颜色名称 | 颜色值 | 使用位置 | 用途说明 |
|---------|--------|----------|----------|
| `white` | `#FFFFFF` | themes.xml, activity_main.xml | 白色，用于主题定义和哨兵功能文字 |
| `black` | `#000000` | - | 黑色，基础颜色定义 |
| `grey` | `#F5F5F5` | - | 灰色，基础颜色定义 |

### 2. 日夜模式适配颜色

#### 2.1 文本颜色适配
**日间模式** (`values/colors.xml`):
| 颜色名称 | 颜色值 | 使用位置 | 用途说明 |
|---------|--------|----------|----------|
| `text_primary_adaptive` | `#000000` | ThemeManager.java, ThemeApplier.java | 主要文本颜色：日间为黑色 |
| `text_secondary_adaptive` | `#666666` | ThemeManager.java, ThemeApplier.java | 次要文本颜色：日间为深灰色 |
| `text_adaptive` | `@color/text_primary_adaptive` | activity_main.xml, item_video_record.xml | 兼容性文本颜色，指向主要文本颜色 |

**夜间模式** (`values-night/colors.xml`):
| 颜色名称 | 颜色值 | 使用位置 | 用途说明 |
|---------|--------|----------|----------|
| `text_primary_adaptive` | `#FFFFFF` | ThemeManager.java, ThemeApplier.java | 主要文本颜色：夜间为白色 |
| `text_secondary_adaptive` | `#CCCCCC` | ThemeManager.java, ThemeApplier.java | 次要文本颜色：夜间为浅灰色 |
| `text_adaptive` | `@color/text_primary_adaptive` | activity_main.xml, item_video_record.xml | 兼容性文本颜色，指向主要文本颜色 |

#### 2.2 背景颜色适配
**日间模式** (`values/colors.xml`):
| 颜色名称 | 颜色值 | 使用位置 | 用途说明 |
|---------|--------|----------|----------|
| `background_primary_adaptive` | `#DEE2E5` | ThemeManager.java, ThemeApplier.java | 主要背景颜色：日间为浅灰色 |
| `background_secondary_adaptive` | `#DEE2E5` | ThemeManager.java | 次要背景颜色：日间为浅灰色 |
| `container_background_adaptive` | `#F5F5F5` | ThemeManager.java, ThemeApplier.java | 容器背景颜色：日间为浅灰色 |
| `background_adaptive` | `@color/background_primary_adaptive` | - | 兼容性背景颜色，指向主要背景颜色 |

**夜间模式** (`values-night/colors.xml`):
| 颜色名称 | 颜色值 | 使用位置 | 用途说明 |
|---------|--------|----------|----------|
| `background_primary_adaptive` | `#000000` | ThemeManager.java, ThemeApplier.java | 主要背景颜色：夜间为黑色 |
| `background_secondary_adaptive` | `#000000` | ThemeManager.java | 次要背景颜色：夜间为黑色 |
| `container_background_adaptive` | `#1A1A1A` | ThemeManager.java, ThemeApplier.java | 容器背景颜色：夜间为深灰色 |
| `background_adaptive` | `@color/background_primary_adaptive` | - | 兼容性背景颜色，指向主要背景颜色 |

### 3. 按钮颜色系统

#### 3.1 按钮文字颜色
**日间模式** (`values/colors.xml`):
| 颜色名称 | 颜色值 | 使用位置 | 用途说明 |
|---------|--------|----------|----------|
| `button_text_selected_adaptive` | `#000000` | ThemeManager.java, ThemeApplier.java | 选中按钮文字：日间为黑色 |
| `button_text_unselected_adaptive` | `#808080` | ThemeManager.java, ThemeApplier.java | 未选中按钮文字：日间为灰色 |
| `button_text_unselected` | `@color/button_text_unselected_adaptive` | activity_main.xml | 兼容性按钮文字颜色 |

**夜间模式** (`values-night/colors.xml`):
| 颜色名称 | 颜色值 | 使用位置 | 用途说明 |
|---------|--------|----------|----------|
| `button_text_selected_adaptive` | `#FFFFFF` | ThemeManager.java, ThemeApplier.java | 选中按钮文字：夜间为白色 |
| `button_text_unselected_adaptive` | `#808080` | ThemeManager.java, ThemeApplier.java | 未选中按钮文字：夜间为灰色 |
| `button_text_unselected` | `@color/button_text_unselected_adaptive` | activity_main.xml | 兼容性按钮文字颜色 |

#### 3.2 按钮背景和边框颜色
**日间模式** (`values/colors.xml`):
| 颜色名称 | 颜色值 | 使用位置 | 用途说明 |
|---------|--------|----------|----------|
| `button_text_color` | `@color/colorPrimary` | themes.xml | 按钮文字颜色，指向主色调 |
| `button_background_selected` | `#44676767` | button_background.xml | 选中按钮背景：半透明灰色 |
| `button_outline_color` | `@color/colorPrimary` | button_outline.xml | 按钮边框颜色，指向主色调 |

**夜间模式** (`values-night/colors.xml`):
| 颜色名称 | 颜色值 | 使用位置 | 用途说明 |
|---------|--------|----------|----------|
| `button_text_color` | `#FFFFFF` | themes.xml | 按钮文字颜色：夜间为白色 |
| `button_background_selected` | `#44676767` | button_background.xml | 选中按钮背景：半透明灰色 |
| `button_outline_color` | `#FFFFFF` | button_outline.xml | 按钮边框颜色：夜间为白色 |

### 4. 系统UI颜色

#### 4.1 状态栏和导航栏
**日间模式** (`values/colors.xml`):
| 颜色名称 | 颜色值 | 使用位置 | 用途说明 |
|---------|--------|----------|----------|
| `status_bar_color` | `#DEE2E5` | themes.xml, ThemeManager.java | 状态栏颜色：日间为浅灰色 |
| `navigation_bar_color` | `@color/colorPrimaryDark` | themes.xml | 导航栏颜色，指向深色主色调 |
| `window_background` | `#DEE2E5` | themes.xml | 窗口背景颜色：日间为浅灰色 |

**夜间模式** (`values-night/colors.xml`):
| 颜色名称 | 颜色值 | 使用位置 | 用途说明 |
|---------|--------|----------|----------|
| `status_bar_color` | `#000000` | themes.xml, ThemeManager.java | 状态栏颜色：夜间为黑色 |
| `navigation_bar_color` | `#000000` | themes.xml | 导航栏颜色：夜间为黑色 |
| `window_background` | `#000000` | themes.xml | 窗口背景颜色：夜间为黑色 |

## 📱 具体使用场景

### 1. 布局文件中的颜色使用

#### 1.1 activity_main.xml
| 控件类型 | 控件ID/描述 | 使用颜色 | 用途 |
|---------|------------|----------|------|
| TextView | 哨兵功能文字 | `@color/white` | 哨兵功能说明文字（固定白色） |
| Button | 摄像头筛选"全部"按钮 | `@color/text_adaptive` | 筛选按钮文字（适配主题） |
| Button | 摄像头筛选其他按钮 | `@color/button_text_unselected` | 未选中筛选按钮文字 |
| Button | 时间筛选"全部"按钮 | `@color/text_adaptive` | 时间筛选按钮文字（适配主题） |
| Button | 时间筛选其他按钮 | `@color/button_text_unselected` | 未选中时间筛选按钮文字 |

#### 1.2 item_video_record.xml
| 控件类型 | 控件ID | 使用颜色 | 用途 |
|---------|--------|----------|------|
| TextView | tv_camera_direction | `@color/text_adaptive` | 摄像头方向文字（适配主题） |
| TextView | tv_record_time | `@color/text_secondary_adaptive` | 录制时间文字（次要文本） |

### 2. Drawable文件中的颜色使用

#### 2.1 button_background.xml
| 属性 | 使用颜色 | 用途 |
|------|----------|------|
| solid color | `@color/button_background_selected` | 选中按钮的背景颜色 |

#### 2.2 button_outline.xml
| 属性 | 使用颜色 | 用途 |
|------|----------|------|
| stroke color | `@color/button_outline_color` | 按钮边框颜色（适配主题） |

### 3. Java代码中的颜色使用

#### 3.1 ThemeManager.java
| 颜色属性 | 日间模式值 | 夜间模式值 | 用途 |
|---------|-----------|-----------|------|
| textPrimary | Color.BLACK | Color.WHITE | 主要文本颜色 |
| textSecondary | #666666 | #CCCCCC | 次要文本颜色 |
| backgroundPrimary | #DEE2E5 | Color.BLACK | 主要背景颜色 |
| containerBackground | #F5F5F5 | #1A1A1A | 容器背景颜色 |
| buttonTextSelected | Color.BLACK | Color.WHITE | 选中按钮文字颜色 |
| buttonTextUnselected | #808080 | #808080 | 未选中按钮文字颜色 |
| statusBarColor | #DEE2E5 | Color.BLACK | 状态栏颜色 |

#### 3.2 MainActivity.java 中的应用
| 应用方法 | 目标控件 | 使用颜色属性 | 用途 |
|---------|----------|-------------|------|
| applyThemeToMainComponents() | 左侧控制面板 | backgroundPrimary | 主要背景 |
| applyThemeToMainComponents() | 右侧预览区域 | backgroundPrimary | 主要背景 |
| applyThemeToPageButtons() | 页面切换按钮 | buttonTextSelected/Unselected | 按钮文字 |
| applyThemeToTextViews() | 时间显示 | textPrimary | 主要文本 |
| applyThemeToCameraContainers() | 相机容器 | containerBackground | 容器背景 |

## 🔄 兼容性颜色映射

为了保持向后兼容性，项目中定义了一些兼容性颜色：

| 兼容性颜色 | 映射到 | 用途 |
|-----------|--------|------|
| `text_adaptive` | `text_primary_adaptive` | 保持旧代码兼容性 |
| `background_adaptive` | `background_primary_adaptive` | 保持旧代码兼容性 |
| `button_text_unselected` | `button_text_unselected_adaptive` | 保持旧代码兼容性 |

## 📝 使用建议

1. **新增UI组件**：优先使用 `*_adaptive` 系列颜色，确保自动适配日夜模式
2. **文本颜色**：主要文本使用 `text_primary_adaptive`，次要文本使用 `text_secondary_adaptive`
3. **背景颜色**：主要背景使用 `background_primary_adaptive`，容器背景使用 `container_background_adaptive`
4. **按钮颜色**：选中状态使用 `button_text_selected_adaptive`，未选中使用 `button_text_unselected_adaptive`
5. **系统UI**：状态栏使用 `status_bar_color`，导航栏使用 `navigation_bar_color`

## 🎯 颜色使用统计

### 按使用频率排序
1. **text_primary_adaptive** - 使用最频繁的文本颜色
   - ThemeManager.java (主题管理)
   - ThemeApplier.java (主题应用)
   - activity_main.xml (通过text_adaptive引用)
   - item_video_record.xml (通过text_adaptive引用)

2. **background_primary_adaptive** - 使用最频繁的背景颜色
   - ThemeManager.java (主题管理)
   - ThemeApplier.java (主题应用)
   - MainActivity.java (通过ThemeApplier应用)

3. **button_text_unselected_adaptive** - 按钮文字颜色
   - activity_main.xml (摄像头筛选按钮、时间筛选按钮)
   - ThemeManager.java (主题管理)
   - ThemeApplier.java (主题应用)

### 按组件类型分类
#### UI组件颜色使用
- **TextView**: text_adaptive, text_secondary_adaptive
- **Button**: button_text_unselected, text_adaptive
- **CardView**: container_background_adaptive (通过ThemeApplier)
- **LinearLayout**: background_primary_adaptive (通过ThemeApplier)

#### 系统UI颜色使用
- **StatusBar**: status_bar_color (通过MainActivity.setupStatusBar())
- **NavigationBar**: navigation_bar_color (themes.xml)
- **Window**: window_background (themes.xml)

## 🔧 维护指南

### 添加新颜色的步骤
1. 在 `values/colors.xml` 中定义日间模式颜色
2. 在 `values-night/colors.xml` 中定义夜间模式颜色
3. 在 `ThemeManager.ThemeColors` 中添加对应属性
4. 在 `ThemeManager.loadThemeColors()` 中加载颜色
5. 在 `ThemeApplier` 中添加应用方法（如需要）

### 修改现有颜色的注意事项
1. 确保日夜模式都有对应定义
2. 检查所有引用该颜色的地方
3. 测试主题切换功能
4. 更新本文档

### 颜色命名规范
- 使用描述性名称：`text_primary_adaptive`
- 添加适配后缀：`*_adaptive`
- 保持一致性：`button_text_selected_adaptive`

## 🚨 注意事项

### 硬编码颜色警告
以下位置仍使用硬编码颜色，需要特别注意：
1. **播放器控制栏**: 保持白色文字（因为有黑色半透明背景）
2. **Ripple效果**: button_background.xml中的ripple颜色 `#33FFFFFF`
3. **特殊UI**: 某些特定场景可能需要固定颜色

### 兼容性考虑
1. 保留兼容性颜色映射，避免破坏现有代码
2. 新代码优先使用 `*_adaptive` 系列颜色
3. 逐步迁移旧代码到新的颜色系统

---

**文档版本**: 1.0
**最后更新**: 2025-01-04
**维护者**: ThemeRefactor
