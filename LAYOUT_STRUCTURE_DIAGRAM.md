# 哨兵监控页面布局结构图

## 修改前的布局
```
┌─────────────────────────────────────────────────────────────────────────────┐
│                           哨兵监控页面 (左侧25%)                              │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│                          [居中对齐的内容]                                    │
│                                                                             │
│                    ┌─────────────────────────┐                             │
│                    │  自动启动哨兵功能  [开关] │                             │
│                    └─────────────────────────┘                             │
│                                                                             │
│                           ┌─────────┐                                      │
│                           │ 录制按钮 │                                      │
│                           └─────────┘                                      │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 修改后的布局
```
┌─────────────────────────────────────────────────────────────────────────────┐
│                           哨兵监控页面 (左侧25%)                              │
├─────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │                      录像状态显示区域                                    │ │
│ │ ● 录像状态：录制中                                                      │ │
│ │ 录制时长：00:05:23                                                      │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│                     [固定位置的控制区域]                                     │
│                                                                             │
│                    ┌─────────────────────────┐                             │
│                    │  自动启动哨兵功能  [开关] │                             │
│                    └─────────────────────────┘                             │
│                                                                             │
│                           ┌─────────┐                                      │
│                           │ 录制按钮 │                                      │
│                           └─────────┘                                      │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 关键改进点

### 1. 录像状态显示区域
- **位置**: 页面顶部，独立容器
- **内容**: 
  - 状态图标 + 文字描述
  - 录制时长实时显示
- **样式**: 半透明背景，圆角边框

### 2. 固定按钮位置
- **方法**: 使用LinearLayout的layout_weight属性
- **效果**: 自动启动哨兵功能按钮位置不再受其他元素影响
- **布局**: 中间控制区域使用flex布局，确保按钮居中且位置稳定

### 3. 状态指示器
```
录制中状态:  ● (红色圆点) + "录像状态：录制中"
未录制状态:  ○ (灰色圆圈) + "录像状态：未录制"
```

### 4. 响应式设计
- 支持日夜模式主题切换
- 文字颜色根据录制状态动态调整
- 布局在不同屏幕尺寸下保持稳定

## 技术实现要点

### 布局结构
```xml
<LinearLayout orientation="vertical">
    <!-- 录像状态显示区域 -->
    <LinearLayout id="recording_status_container">
        <ImageView id="iv_recording_status_icon" />
        <TextView id="tv_recording_status" />
        <TextView id="tv_recording_duration" />
    </LinearLayout>
    
    <!-- 中间控制区域 (使用layout_weight=1) -->
    <LinearLayout layout_weight="1" gravity="center">
        <!-- 哨兵自动模式开关 -->
        <LinearLayout>
            <TextView>自动启动哨兵功能</TextView>
            <Switch id="switch_sentry_auto" />
        </LinearLayout>
        
        <!-- 录制控制按钮 -->
        <Button id="btn_all_cameras" />
    </LinearLayout>
</LinearLayout>
```

### 状态管理
- MainActivity观察MainViewModel的录制状态和时长
- 实时更新UI显示
- 不干扰现有录像控制逻辑
