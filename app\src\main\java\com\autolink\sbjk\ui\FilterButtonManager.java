package com.autolink.sbjk.ui;

import android.content.res.Configuration;
import android.graphics.Color;
import android.widget.Button;

import com.autolink.sbjk.R;
import com.autolink.sbjk.common.util.LogUtil;
import com.autolink.sbjk.common.theme.ThemeManager;
import com.autolink.sbjk.common.theme.ThemeApplier;

import java.util.ArrayList;
import java.util.List;

/**
 * 筛选按钮管理器
 * 管理摄像头和时间筛选按钮的状态和点击事件
 *
 * 功能说明：
 * - 实现ThemeManager.ThemeChangeListener接口，统一处理主题变化
 * - 使用ThemeManager集中管理主题状态
 * - 使用ThemeApplier统一应用按钮主题
 * - 优化按钮状态更新机制，记录选中索引避免遍历查找
 */
public class FilterButtonManager implements ThemeManager.ThemeChangeListener {
    
    private static final String TAG = "FilterButtonManager";
    
    // 筛选回调接口
    public interface OnFilterChangeListener {
        void onCameraFilterChanged(String cameraFilter);
        void onTimeFilterChanged(String timeFilter);
    }
    
    private OnFilterChangeListener filterListener;

    // 主题管理相关
    private ThemeManager.ThemeColors currentColors;
    private int selectedCameraIndex = 0;  // 记录选中的摄像头按钮索引，避免遍历查找
    private int selectedTimeIndex = 0;    // 记录选中的时间按钮索引，避免遍历查找

    // 摄像头筛选按钮
    private final List<Button> cameraButtons = new ArrayList<>();
    private final String[] cameraFilters = {"全部", "前视", "后视", "左视", "右视"};
    private String currentCameraFilter = "全部";

    // 时间筛选按钮
    private final List<Button> timeButtons = new ArrayList<>();
    private final String[] timeFilters = {"全部", "月", "日", "时"};
    private String currentTimeFilter = "全部";

    /**
     * 构造函数 - 注册主题变化监听器
     */
    public FilterButtonManager() {
        // 注册主题变化监听器
        ThemeManager.getInstance().addThemeChangeListener(this);
        LogUtil.d(TAG, "FilterButtonManager created and theme listener registered");
    }

    // ===== 主题管理接口实现 =====

    /**
     * 主题变化回调 - ThemeManager.ThemeChangeListener接口实现
     *
     * 替换原有的重复主题检测逻辑，统一在此处理按钮主题变化
     *
     * @param isDarkMode 是否为深色模式
     * @param colors 当前主题颜色配置
     */
    @Override
    public void onThemeChanged(boolean isDarkMode, ThemeManager.ThemeColors colors) {
        currentColors = colors;
        LogUtil.d(TAG, "Theme changed to: " + (isDarkMode ? "Dark" : "Light") + " mode");

        // 立即应用新主题到所有按钮
        updateAllButtonColors();
    }

    /**
     * 应用主题（外部调用接口，兼容原有调用方式）
     *
     * @param colors 主题颜色配置
     */
    public void applyTheme(ThemeManager.ThemeColors colors) {
        currentColors = colors;
        updateAllButtonColors();
        LogUtil.d(TAG, "Theme applied externally");
    }

    /**
     * 设置筛选监听器
     */
    public void setOnFilterChangeListener(OnFilterChangeListener listener) {
        this.filterListener = listener;
    }
    
    /**
     * 初始化摄像头筛选按钮
     */
    public void initCameraButtons(Button btnAll, Button btnFront, Button btnBack, Button btnLeft, Button btnRight) {
        cameraButtons.clear();
        cameraButtons.add(btnAll);
        cameraButtons.add(btnFront);
        cameraButtons.add(btnBack);
        cameraButtons.add(btnLeft);
        cameraButtons.add(btnRight);
        
        // 设置点击事件
        for (int i = 0; i < cameraButtons.size(); i++) {
            final int index = i;
            final String filter = cameraFilters[i];
            cameraButtons.get(i).setOnClickListener(v -> selectCameraFilter(index, filter));
        }
        
        // 设置默认选中状态（不触发回调）
        currentCameraFilter = "全部";
        updateCameraButtonStates(0);

        LogUtil.d(TAG, "摄像头筛选按钮初始化完成");
    }
    
    /**
     * 初始化时间筛选按钮
     */
    public void initTimeButtons(Button btnAll, Button btnMonth, Button btnDay, Button btnHour) {
        timeButtons.clear();
        timeButtons.add(btnAll);
        timeButtons.add(btnMonth);
        timeButtons.add(btnDay);
        timeButtons.add(btnHour);
        
        // 设置点击事件
        for (int i = 0; i < timeButtons.size(); i++) {
            final int index = i;
            final String filter = timeFilters[i];
            timeButtons.get(i).setOnClickListener(v -> selectTimeFilter(index, filter));
        }
        
        // 设置默认选中状态（不触发回调）
        currentTimeFilter = "全部";
        updateTimeButtonStates(0);

        LogUtil.d(TAG, "时间筛选按钮初始化完成");
    }
    
    /**
     * 选择摄像头筛选
     */
    private void selectCameraFilter(int selectedIndex, String filter) {
        if (filter.equals(currentCameraFilter)) {
            return; // 已经是当前选中的筛选，不需要重复处理
        }
        
        currentCameraFilter = filter;
        
        // 更新按钮状态
        updateCameraButtonStates(selectedIndex);
        
        // 通知监听器
        if (filterListener != null) {
            filterListener.onCameraFilterChanged(filter);
        }
        
        LogUtil.d(TAG, "选择摄像头筛选: " + filter);
    }
    
    /**
     * 选择时间筛选
     */
    private void selectTimeFilter(int selectedIndex, String filter) {
        if (filter.equals(currentTimeFilter)) {
            return; // 已经是当前选中的筛选，不需要重复处理
        }
        
        currentTimeFilter = filter;
        
        // 更新按钮状态
        updateTimeButtonStates(selectedIndex);
        
        // 通知监听器
        if (filterListener != null) {
            filterListener.onTimeFilterChanged(filter);
        }
        
        LogUtil.d(TAG, "选择时间筛选: " + filter);
    }
    
    /**
     * 【重构】更新摄像头按钮状态
     *
     * 移除重复的主题检测逻辑，使用缓存的ThemeColors
     * 使用ThemeApplier统一应用按钮主题
     */
    private void updateCameraButtonStates(int selectedIndex) {
        selectedCameraIndex = selectedIndex; // 记录选中索引，优化refreshButtonColors性能

        for (int i = 0; i < cameraButtons.size(); i++) {
            Button button = cameraButtons.get(i);
            boolean isSelected = (i == selectedIndex);

            // 设置背景资源
            if (isSelected) {
                button.setBackgroundResource(R.drawable.button_background);
            } else {
                button.setBackgroundResource(R.drawable.button_outline);
            }

            // 使用ThemeApplier应用文字颜色，替换原有的硬编码颜色和重复主题检测
            if (currentColors != null) {
                ThemeApplier.applyToButton(button, currentColors, isSelected);
            }
        }

        LogUtil.d(TAG, "Updated camera button states, selected index: " + selectedIndex);
    }
    
    /**
     * 【重构】更新时间按钮状态
     *
     * 移除重复的主题检测逻辑，使用缓存的ThemeColors
     * 使用ThemeApplier统一应用按钮主题
     */
    private void updateTimeButtonStates(int selectedIndex) {
        selectedTimeIndex = selectedIndex; // 记录选中索引，优化refreshButtonColors性能

        for (int i = 0; i < timeButtons.size(); i++) {
            Button button = timeButtons.get(i);
            boolean isSelected = (i == selectedIndex);

            // 设置背景资源
            if (isSelected) {
                button.setBackgroundResource(R.drawable.button_background);
            } else {
                button.setBackgroundResource(R.drawable.button_outline);
            }

            // 使用ThemeApplier应用文字颜色，替换原有的硬编码颜色和重复主题检测
            if (currentColors != null) {
                ThemeApplier.applyToButton(button, currentColors, isSelected);
            }
        }

        LogUtil.d(TAG, "Updated time button states, selected index: " + selectedIndex);
    }
    
    /**
     * 程序化设置摄像头筛选（不触发回调）
     */
    public void setCameraFilter(String filter) {
        for (int i = 0; i < cameraFilters.length; i++) {
            if (cameraFilters[i].equals(filter)) {
                currentCameraFilter = filter;
                updateCameraButtonStates(i);
                break;
            }
        }
    }
    
    /**
     * 程序化设置时间筛选（不触发回调）
     */
    public void setTimeFilter(String filter) {
        for (int i = 0; i < timeFilters.length; i++) {
            if (timeFilters[i].equals(filter)) {
                currentTimeFilter = filter;
                updateTimeButtonStates(i);
                break;
            }
        }
    }
    
    /**
     * 重置所有筛选
     */
    public void resetFilters() {
        selectCameraFilter(0, "全部");
        selectTimeFilter(0, "全部");
        LogUtil.d(TAG, "重置所有筛选");
    }

    /**
     * 强制重置到初始状态（不触发回调）
     * 用于生命周期开始时确保全新状态
     */
    public void forceResetToInitialState() {
        // 重置摄像头筛选状态
        currentCameraFilter = "全部";
        if (!cameraButtons.isEmpty()) {
            updateCameraButtonStates(0);
        }

        // 重置时间筛选状态
        currentTimeFilter = "全部";
        if (!timeButtons.isEmpty()) {
            updateTimeButtonStates(0);
        }

        LogUtil.d(TAG, "强制重置筛选器到初始状态");
    }
    
    /**
     * 获取当前摄像头筛选
     */
    public String getCurrentCameraFilter() {
        return currentCameraFilter;
    }
    
    /**
     * 获取当前时间筛选
     */
    public String getCurrentTimeFilter() {
        return currentTimeFilter;
    }
    
    /**
     * 获取筛选描述
     */
    public String getFilterDescription() {
        if ("全部".equals(currentCameraFilter) && "全部".equals(currentTimeFilter)) {
            return "显示所有录像";
        } else if ("全部".equals(currentCameraFilter)) {
            return "显示" + currentTimeFilter + "的录像";
        } else if ("全部".equals(currentTimeFilter)) {
            return "显示" + currentCameraFilter + "的录像";
        } else {
            return "显示" + currentCameraFilter + currentTimeFilter + "的录像";
        }
    }

    /**
     * 【重构】更新所有按钮颜色
     *
     * 替换原有的refreshButtonColors()方法，优化性能：
     * - 使用记录的选中索引，避免遍历查找
     * - 统一调用updateXXXButtonStates方法
     */
    private void updateAllButtonColors() {
        if (currentColors != null) {
            // 直接使用记录的索引，避免遍历查找
            updateCameraButtonStates(selectedCameraIndex);
            updateTimeButtonStates(selectedTimeIndex);

            LogUtil.d(TAG, "All button colors updated using cached indices");
        } else {
            LogUtil.w(TAG, "Current colors is null, skipping button color update");
        }
    }



    /**
     * 清理资源
     *
     * 在FilterButtonManager不再使用时调用，移除主题监听器防止内存泄漏
     */
    public void cleanup() {
        ThemeManager.getInstance().removeThemeChangeListener(this);
        cameraButtons.clear();
        timeButtons.clear();
        LogUtil.d(TAG, "FilterButtonManager cleaned up, theme listener removed");
    }
}
