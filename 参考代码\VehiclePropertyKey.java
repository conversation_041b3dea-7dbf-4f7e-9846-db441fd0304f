package com.autolink.sbjk.utils;

import android.util.Log;

import java.util.HashMap;
import java.util.Map;

/**
 * 车辆属性键值定义
 */
public class VehiclePropertyKey {
    private static final String TAG = "VehicleService";
    
    // 电源状态
    public static final VehiclePropertyKey KeyVehiclePropertyKeySts = new VehiclePropertyKey(2072, "钥匙状态");
    public static final VehiclePropertyKey KeyVehiclePropertyEngineSts = new VehiclePropertyKey(2073, "发动机状态");
    
    // 档位状态
    public static final VehiclePropertyKey KeyVehiclePropertyGearPosition = new VehiclePropertyKey(1033, "当前档位");
    
    // 车速
    public static final VehiclePropertyKey KeyVehiclePropertySpeed = new VehiclePropertyKey(1089, "车速");
    
    // 电池相关属性
    public static final VehiclePropertyKey KeyBatteryMaxSingleVoltageNum = new VehiclePropertyKey(1055, "电池最高单体电压编号");
    public static final VehiclePropertyKey KeyBatteryStatus = new VehiclePropertyKey(1050, "电池状态");
    public static final VehiclePropertyKey KeyBatteryVoltage = new VehiclePropertyKey(1082, "电池电压");
    public static final VehiclePropertyKey KeyBatteryCurrent = new VehiclePropertyKey(1081, "电池电流");
    
    private static final Map<Integer, VehiclePropertyKey> keyMap = new HashMap<>();
    
    public static final VehiclePropertyKey INSTANCE = new VehiclePropertyKey(0, "实例");
    
    static {
        // 确保静态属性被添加到映射中
        registerKey(KeyVehiclePropertyKeySts);
        registerKey(KeyVehiclePropertyEngineSts);
        registerKey(KeyVehiclePropertyGearPosition);
        registerKey(KeyVehiclePropertySpeed);
        
        // 注册电池相关属性
        registerKey(KeyBatteryMaxSingleVoltageNum);
        registerKey(KeyBatteryStatus);
        registerKey(KeyBatteryVoltage);
        registerKey(KeyBatteryCurrent);
        
        Log.i(TAG, "VehiclePropertyKey初始化完成，注册了 " + keyMap.size() + " 个属性");
        // 输出所有注册的属性ID方便调试
        for (Map.Entry<Integer, VehiclePropertyKey> entry : keyMap.entrySet()) {
            Log.i(TAG, "注册属性: " + entry.getValue().getDesc() + " (ID=0x" + Integer.toHexString(entry.getKey()) + ", 十进制=" + entry.getKey() + ")");
        }
    }
    
    private static void registerKey(VehiclePropertyKey key) {
        keyMap.put(key.getValue(), key);
    }
    
    private final int value;
    private final String desc;
    
    private VehiclePropertyKey(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }
    
    public int getValue() {
        return value;
    }
    
    public String getDesc() {
        return desc;
    }
    
    public VehiclePropertyKey valueOf(int value) {
        VehiclePropertyKey key = keyMap.get(value);
        if (key == null) {
            Log.w(TAG, "未找到属性键值: 0x" + Integer.toHexString(value) + " (十进制=" + value + ")");
        }
        return key;
    }
    
    @Override
    public String toString() {
        return desc + " (ID=0x" + Integer.toHexString(value) + ", 十进制=" + value + ")";
    }
} 