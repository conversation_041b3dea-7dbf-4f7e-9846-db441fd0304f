package com.autolink.sbjk.lifecycle;

import android.app.Application;
import android.content.Context;
import android.os.Bundle;
import androidx.lifecycle.ViewModelProvider;
import androidx.test.core.app.ApplicationProvider;

import com.autolink.sbjk.viewmodel.MainViewModel;
import com.autolink.sbjk.viewmodel.MainViewModelFactory;
import com.autolink.sbjk.viewmodel.CameraPreviewViewModel;
import com.autolink.sbjk.viewmodel.CameraPreviewViewModelFactory;
import com.autolink.sbjk.di.DIContainer;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.annotation.Config;

import static org.junit.Assert.*;

/**
 * 生命周期Bug修复验证测试
 */
@RunWith(RobolectricTestRunner.class)
@Config(sdk = 30)
public class LifecycleBugFixTest {

    private Context context;
    private Application application;

    @Before
    public void setUp() {
        context = ApplicationProvider.getApplicationContext();
        application = (Application) context;
        
        // 清理DI容器状态
        DIContainer.clearInstances();
    }

    @After
    public void tearDown() {
        // 清理DI容器状态
        DIContainer.clearInstances();
    }

    /**
     * 测试MainViewModelFactory是否正确创建ViewModel
     */
    @Test
    public void testMainViewModelFactory() {
        MainViewModelFactory factory = new MainViewModelFactory(context);
        
        // 验证工厂能够创建ViewModel
        MainViewModel viewModel = (MainViewModel) factory.create(MainViewModel.class);
        assertNotNull("MainViewModel should be created", viewModel);
        
        // 验证创建的是同一个类型
        assertTrue("Should be MainViewModel instance", viewModel instanceof MainViewModel);
    }

    /**
     * 测试CameraPreviewViewModelFactory是否正确创建ViewModel
     */
    @Test
    public void testCameraPreviewViewModelFactory() {
        CameraPreviewViewModelFactory factory = new CameraPreviewViewModelFactory(context);
        
        // 验证工厂能够创建ViewModel
        CameraPreviewViewModel viewModel = (CameraPreviewViewModel) factory.create(CameraPreviewViewModel.class);
        assertNotNull("CameraPreviewViewModel should be created", viewModel);
        
        // 验证创建的是同一个类型
        assertTrue("Should be CameraPreviewViewModel instance", viewModel instanceof CameraPreviewViewModel);
    }

    /**
     * 测试ViewModelFactory处理未知类型
     */
    @Test(expected = IllegalArgumentException.class)
    public void testMainViewModelFactoryWithUnknownClass() {
        MainViewModelFactory factory = new MainViewModelFactory(context);
        
        // 尝试创建未知类型的ViewModel，应该抛出异常
        factory.create(UnknownViewModel.class);
    }

    /**
     * 测试状态保存键值常量
     */
    @Test
    public void testStateKeys() {
        // 验证状态保存键值是否定义正确
        // 这些常量在MainActivity中定义，这里只是验证概念
        String sentryModeKey = "sentry_mode_enabled";
        String currentPageKey = "current_page_sentinel";
        
        assertNotNull("Sentry mode key should not be null", sentryModeKey);
        assertNotNull("Current page key should not be null", currentPageKey);
        assertFalse("Keys should not be empty", sentryModeKey.isEmpty());
        assertFalse("Keys should not be empty", currentPageKey.isEmpty());
    }

    /**
     * 测试Bundle状态保存和恢复
     */
    @Test
    public void testBundleStateHandling() {
        Bundle bundle = new Bundle();
        
        // 模拟状态保存
        boolean sentryMode = true;
        boolean currentPage = false;
        bundle.putBoolean("sentry_mode_enabled", sentryMode);
        bundle.putBoolean("current_page_sentinel", currentPage);
        
        // 模拟状态恢复
        boolean restoredSentryMode = bundle.getBoolean("sentry_mode_enabled", false);
        boolean restoredCurrentPage = bundle.getBoolean("current_page_sentinel", true);
        
        assertEquals("Sentry mode should be restored correctly", sentryMode, restoredSentryMode);
        assertEquals("Current page should be restored correctly", currentPage, restoredCurrentPage);
    }

    /**
     * 测试默认值处理
     */
    @Test
    public void testDefaultValues() {
        Bundle emptyBundle = new Bundle();
        
        // 测试默认值
        boolean defaultSentryMode = emptyBundle.getBoolean("sentry_mode_enabled", false);
        boolean defaultCurrentPage = emptyBundle.getBoolean("current_page_sentinel", true);
        
        assertFalse("Default sentry mode should be false", defaultSentryMode);
        assertTrue("Default current page should be true", defaultCurrentPage);
    }

    // 用于测试的虚拟ViewModel类
    private static class UnknownViewModel extends androidx.lifecycle.ViewModel {
        // 空实现
    }
}
