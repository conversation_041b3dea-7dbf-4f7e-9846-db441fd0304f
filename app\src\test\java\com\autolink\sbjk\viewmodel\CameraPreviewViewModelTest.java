package com.autolink.sbjk.viewmodel;

import android.app.Application;
import android.view.Surface;

import androidx.arch.core.executor.testing.InstantTaskExecutorRule;
import androidx.lifecycle.Observer;

import com.autolink.sbjk.common.constant.CameraConstants;
import com.autolink.sbjk.di.DIContainer;
import com.autolink.sbjk.model.repository.CameraRepository;
import com.autolink.sbjk.model.repository.RecordingRepository;

import org.junit.After;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.RuntimeEnvironment;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * CameraPreviewViewModel单元测试
 */
@RunWith(RobolectricTestRunner.class)
public class CameraPreviewViewModelTest {
    
    @Rule
    public InstantTaskExecutorRule instantTaskExecutorRule = new InstantTaskExecutorRule();
    
    @Mock
    private CameraRepository mockCameraRepository;
    
    @Mock
    private RecordingRepository mockRecordingRepository;
    
    @Mock
    private Observer<Boolean> mockRecordingObserver;
    
    @Mock
    private Observer<String> mockCameraIdObserver;
    
    @Mock
    private Observer<Boolean> mockCameraReadyObserver;
    
    @Mock
    private Observer<String> mockDurationObserver;
    
    @Mock
    private Observer<String> mockErrorObserver;
    
    @Mock
    private Observer<String> mockSuccessObserver;
    
    private CameraPreviewViewModel viewModel;
    private Application application;
    
    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        application = RuntimeEnvironment.getApplication();
        
        // 使用依赖注入创建ViewModel实例
        viewModel = DIContainer.provideCameraPreviewViewModel(mockCameraRepository, mockRecordingRepository);
        
        // 设置观察者
        viewModel.isRecording.observeForever(mockRecordingObserver);
        viewModel.currentCameraId.observeForever(mockCameraIdObserver);
        viewModel.isCameraReady.observeForever(mockCameraReadyObserver);
        viewModel.recordingDuration.observeForever(mockDurationObserver);
        viewModel.errorMessage.observeForever(mockErrorObserver);
        viewModel.successMessage.observeForever(mockSuccessObserver);
    }
    
    @After
    public void tearDown() {
        // 清理DI容器实例
        DIContainer.clearInstances();
    }
    
    @Test
    public void testInitialState() {
        // 验证初始状态
        assertFalse("初始状态应该不在录制", viewModel.isCurrentlyRecording());
        assertEquals("初始相机ID应该是前置摄像头", 
                    CameraConstants.CAMERA_FRONT, viewModel.getCurrentCameraId());
        assertFalse("初始状态相机应该未准备就绪", viewModel.isCameraCurrentlyReady());
    }
    
    @Test
    public void testSetCurrentCameraId() {
        // 测试设置有效的相机ID
        viewModel.setCurrentCameraId(CameraConstants.CAMERA_BACK);
        
        // 验证相机ID被设置
        assertEquals("相机ID应该被正确设置", 
                    CameraConstants.CAMERA_BACK, viewModel.getCurrentCameraId());
        
        // 验证观察者被调用
        verify(mockCameraIdObserver).onChanged(CameraConstants.CAMERA_BACK);
    }
    
    @Test
    public void testSetInvalidCameraId() {
        // 测试设置无效的相机ID
        viewModel.setCurrentCameraId("invalid_camera_id");
        
        // 验证相机ID没有被改变
        assertEquals("无效相机ID不应该被设置", 
                    CameraConstants.CAMERA_FRONT, viewModel.getCurrentCameraId());
        
        // 验证错误观察者被调用
        verify(mockErrorObserver).onChanged("无效的相机ID");
    }
    
    @Test
    public void testPreviewSurfaceAvailable() {
        // 准备测试数据
        Surface mockSurface = mock(Surface.class);
        
        // 执行预览Surface可用
        viewModel.onPreviewSurfaceAvailable(mockSurface);
        
        // 验证Repository方法被调用
        verify(mockCameraRepository).setPreviewSurface(CameraConstants.CAMERA_FRONT, mockSurface);
        
        // 验证相机准备状态被设置为true
        verify(mockCameraReadyObserver).onChanged(true);
    }
    
    @Test
    public void testPreviewSurfaceChanged() {
        // 准备测试数据
        Surface mockSurface = mock(Surface.class);
        
        // 执行预览Surface变化
        viewModel.onPreviewSurfaceChanged(mockSurface);
        
        // 验证Repository方法被调用
        verify(mockCameraRepository).updatePreviewSurface(CameraConstants.CAMERA_FRONT, mockSurface);
    }
    
    @Test
    public void testPreviewSurfaceDestroyed() {
        // 执行预览Surface销毁
        viewModel.onPreviewSurfaceDestroyed();
        
        // 验证Repository方法被调用
        verify(mockCameraRepository).clearPreviewSurface(CameraConstants.CAMERA_FRONT);
        
        // 验证相机准备状态被设置为false
        verify(mockCameraReadyObserver).onChanged(false);
    }
    
    @Test
    public void testStartRecording() {
        // 执行开始录制
        viewModel.startRecording();
        
        // 验证Repository方法被调用
        verify(mockCameraRepository).startRecording(eq(CameraConstants.CAMERA_FRONT), anyString());
        
        // 验证录制状态被设置为true
        verify(mockRecordingObserver).onChanged(true);
        
        // 验证成功消息
        verify(mockSuccessObserver).onChanged("开始录制");
    }
    
    @Test
    public void testStopRecording() {
        // 先开始录制
        viewModel.startRecording();
        
        // 执行停止录制
        viewModel.stopRecording();
        
        // 验证Repository方法被调用
        verify(mockCameraRepository).stopRecording(CameraConstants.CAMERA_FRONT);
        
        // 验证录制状态被设置为false
        verify(mockRecordingObserver).onChanged(false);
        
        // 验证成功消息
        verify(mockSuccessObserver).onChanged("录制已停止");
    }
    
    @Test
    public void testToggleRecording() {
        // 初始状态：未录制，执行切换应该开始录制
        viewModel.toggleRecording();
        
        // 验证开始录制被调用
        verify(mockCameraRepository).startRecording(eq(CameraConstants.CAMERA_FRONT), anyString());
        
        // 模拟录制状态为true
        viewModel.isRecording.observeForever(isRecording -> {
            if (isRecording != null && isRecording) {
                // 再次切换应该停止录制
                viewModel.toggleRecording();
                
                // 验证停止录制被调用
                verify(mockCameraRepository).stopRecording(CameraConstants.CAMERA_FRONT);
            }
        });
    }
    
    @Test
    public void testDependencyInjectionConstructor() {
        // 测试依赖注入构造函数
        CameraPreviewViewModel testViewModel = new CameraPreviewViewModel(mockCameraRepository, mockRecordingRepository);
        
        assertNotNull(testViewModel);
        assertNotNull(testViewModel.isRecording);
        assertNotNull(testViewModel.currentCameraId);
        assertNotNull(testViewModel.isCameraReady);
        assertNotNull(testViewModel.recordingDuration);
        assertNotNull(testViewModel.errorMessage);
        assertNotNull(testViewModel.successMessage);
    }
    
    @Test
    public void testApplicationConstructor() {
        // 测试Application构造函数
        CameraPreviewViewModel testViewModel = new CameraPreviewViewModel(application);
        
        assertNotNull(testViewModel);
        assertEquals("默认相机ID应该是前置摄像头", 
                    CameraConstants.CAMERA_FRONT, testViewModel.getCurrentCameraId());
    }
    
    @Test
    public void testRecordingDurationFormat() {
        // 这个测试需要等待实际的时间更新，在单元测试中比较困难
        // 可以通过反射或者提取格式化方法来测试
        
        // 验证初始录制时长
        assertEquals("初始录制时长应该是00:00:00", 
                    "00:00:00", viewModel.recordingDuration.getValue());
    }
    
    @Test
    public void testErrorHandling() {
        // 测试错误处理机制
        String testError = "Test error message";
        
        // 通过BaseViewModel的方法设置错误
        viewModel.setError(testError);
        
        // 验证错误观察者被调用
        verify(mockErrorObserver).onChanged(testError);
    }
}
