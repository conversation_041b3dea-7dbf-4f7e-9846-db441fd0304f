package com.autolink.sbjk.viewmodel;

import android.content.Context;

import androidx.annotation.NonNull;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider;

import com.autolink.sbjk.repository.PlaybackRepository;

/**
 * PlaybackViewModel的工厂类
 * 负责创建PlaybackViewModel实例并注入依赖
 */
public class PlaybackViewModelFactory implements ViewModelProvider.Factory {
    
    private final Context context;
    
    public PlaybackViewModelFactory(Context context) {
        this.context = context;
    }
    
    @NonNull
    @Override
    public <T extends ViewModel> T create(@NonNull Class<T> modelClass) {
        if (modelClass.isAssignableFrom(PlaybackViewModel.class)) {
            return (T) new PlaybackViewModel();
        }
        throw new IllegalArgumentException("Unknown ViewModel class: " + modelClass.getName());
    }
}
