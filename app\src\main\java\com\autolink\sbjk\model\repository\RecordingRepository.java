package com.autolink.sbjk.model.repository;

import android.content.Context;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.autolink.sbjk.common.constant.CameraConstants;
import com.autolink.sbjk.common.util.LogUtil;
import com.autolink.sbjk.model.datasource.RecordingDataSource;
import com.autolink.sbjk.model.entity.RecordingSession;
import com.autolink.sbjk.model.entity.VideoSegment;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 录制数据仓储
 * 管理录制会话和视频片段数据
 */
public class RecordingRepository {
    
    private static final String TAG = "RecordingRepository";
    private static volatile RecordingRepository instance;
    
    private final RecordingDataSource recordingDataSource;
    private final ExecutorService executorService;
    
    // LiveData for observing recording state
    private final MutableLiveData<List<RecordingSession>> _activeSessions = new MutableLiveData<>();
    public final LiveData<List<RecordingSession>> activeSessions = _activeSessions;
    
    private final MutableLiveData<Long> _totalRecordingTime = new MutableLiveData<>(0L);
    public final LiveData<Long> totalRecordingTime = _totalRecordingTime;
    
    private final MutableLiveData<Integer> _activeSessionCount = new MutableLiveData<>(0);
    public final LiveData<Integer> activeSessionCount = _activeSessionCount;
    
    private RecordingRepository(Context context) {
        this.recordingDataSource = new RecordingDataSource(context);
        this.executorService = Executors.newCachedThreadPool();
        
        LogUtil.d(TAG, "RecordingRepository initialized");
    }
    
    public static RecordingRepository getInstance(Context context) {
        if (instance == null) {
            synchronized (RecordingRepository.class) {
                if (instance == null) {
                    instance = new RecordingRepository(context.getApplicationContext());
                }
            }
        }
        return instance;
    }
    
    /**
     * 创建录制会话
     */
    public void createRecordingSession(String cameraId, String outputPath) {
        executorService.execute(() -> {
            try {
                if (cameraId == null || cameraId.isEmpty()) {
                    LogUtil.e(TAG, "Invalid camera ID for session creation");
                    return;
                }

                if (outputPath == null || outputPath.isEmpty()) {
                    LogUtil.e(TAG, "Invalid output path for session creation");
                    return;
                }

                RecordingSession session = recordingDataSource.createSession(cameraId, outputPath);
                if (session != null) {
                    LogUtil.i(TAG, "Recording session created for camera " + cameraId);
                    updateActiveSessions();
                } else {
                    LogUtil.e(TAG, "Failed to create recording session for camera " + cameraId);
                }
            } catch (Exception e) {
                LogUtil.e(TAG, "Exception creating recording session for camera " + cameraId, e);
            }
        });
    }

    /**
     * 结束录制会话
     */
    public void endRecordingSession(String cameraId) {
        executorService.execute(() -> {
            try {
                if (cameraId == null || cameraId.isEmpty()) {
                    LogUtil.e(TAG, "Invalid camera ID for session ending");
                    return;
                }

                boolean success = recordingDataSource.endSession(cameraId);
                if (success) {
                    LogUtil.i(TAG, "Recording session ended for camera " + cameraId);
                    updateActiveSessions();
                } else {
                    LogUtil.e(TAG, "Failed to end recording session for camera " + cameraId);
                }
            } catch (Exception e) {
                LogUtil.e(TAG, "Exception ending recording session for camera " + cameraId, e);
            }
        });
    }
    
    /**
     * 添加视频片段
     */
    public void addVideoSegment(String cameraId, String segmentPath, long duration) {
        executorService.execute(() -> {
            try {
                if (cameraId == null || cameraId.isEmpty()) {
                    LogUtil.e(TAG, "Invalid camera ID for video segment");
                    return;
                }

                if (segmentPath == null || segmentPath.isEmpty()) {
                    LogUtil.e(TAG, "Invalid segment path for camera " + cameraId);
                    return;
                }

                if (duration <= 0) {
                    LogUtil.w(TAG, "Invalid duration for video segment: " + duration);
                    // 继续处理，但记录警告
                }

                VideoSegment segment = recordingDataSource.addSegment(cameraId, segmentPath, duration);
                if (segment != null) {
                    LogUtil.d(TAG, "Video segment added for camera " + cameraId + ": " + segmentPath);
                    updateTotalRecordingTime();
                } else {
                    LogUtil.e(TAG, "Failed to add video segment for camera " + cameraId);
                }
            } catch (Exception e) {
                LogUtil.e(TAG, "Exception adding video segment for camera " + cameraId, e);
            }
        });
    }
    
    /**
     * 获取相机的录制会话
     */
    public RecordingSession getRecordingSession(String cameraId) {
        return recordingDataSource.getSession(cameraId);
    }
    
    /**
     * 获取所有活跃的录制会话
     */
    public List<RecordingSession> getActiveSessions() {
        return recordingDataSource.getActiveSessions();
    }
    
    /**
     * 获取相机的视频片段列表
     */
    public List<VideoSegment> getVideoSegments(String cameraId) {
        return recordingDataSource.getSegments(cameraId);
    }
    
    /**
     * 获取总录制时长
     */
    public long getTotalRecordingDuration() {
        return recordingDataSource.getTotalDuration();
    }
    
    /**
     * 获取相机录制时长
     */
    public long getCameraRecordingDuration(String cameraId) {
        return recordingDataSource.getCameraDuration(cameraId);
    }
    
    /**
     * 清理过期的录制数据
     */
    public void cleanupExpiredRecordings(long maxAgeMs) {
        executorService.execute(() -> {
            try {
                int cleanedCount = recordingDataSource.cleanupExpired(maxAgeMs);
                LogUtil.i(TAG, "Cleaned up " + cleanedCount + " expired recordings");
            } catch (Exception e) {
                LogUtil.e(TAG, "Exception cleaning up expired recordings", e);
            }
        });
    }
    
    /**
     * 获取存储使用情况
     */
    public void getStorageUsage(StorageUsageCallback callback) {
        executorService.execute(() -> {
            try {
                long totalSize = recordingDataSource.getTotalStorageUsage();
                long availableSpace = recordingDataSource.getAvailableSpace();
                
                if (callback != null) {
                    callback.onStorageUsage(totalSize, availableSpace);
                }
            } catch (Exception e) {
                LogUtil.e(TAG, "Exception getting storage usage", e);
                if (callback != null) {
                    callback.onError(e.getMessage());
                }
            }
        });
    }
    
    /**
     * 更新活跃会话列表
     */
    private void updateActiveSessions() {
        List<RecordingSession> sessions = recordingDataSource.getActiveSessions();
        _activeSessions.postValue(sessions);
        _activeSessionCount.postValue(sessions.size());
    }
    
    /**
     * 更新总录制时长
     */
    private void updateTotalRecordingTime() {
        long totalTime = recordingDataSource.getTotalDuration();
        _totalRecordingTime.postValue(totalTime);
    }
    
    /**
     * 存储使用情况回调接口
     */
    public interface StorageUsageCallback {
        void onStorageUsage(long totalSize, long availableSpace);
        void onError(String error);
    }
    
    /**
     * 释放资源
     */
    public void release() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
        recordingDataSource.release();
        LogUtil.d(TAG, "RecordingRepository released");
    }
}
