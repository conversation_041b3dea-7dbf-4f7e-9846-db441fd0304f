<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- 统一的Sbjk应用主题 - 支持日夜模式自动切换 -->
    <style name="Theme.Sbjk" parent="Theme.AppCompat.DayNight.NoActionBar">
        <!-- Primary brand color - 使用colorPrimary(#3F51B5) -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <!-- Primary dark color - 使用colorPrimaryDark(#303F9F) -->
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <!-- Accent color - 使用colorAccent(#FF4081) -->
        <item name="colorAccent">@color/colorAccent</item>
        
        <!-- 全屏设置 -->
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowTranslucentNavigation">false</item>
        <item name="android:navigationBarColor">@color/colorPrimaryDark</item>
        <item name="android:statusBarColor">@color/background_light</item>
        <item name="android:windowLightStatusBar" tools:targetApi="m">true</item>
        <item name="android:windowBackground">@color/background_light</item>
    </style>
    
    <!-- 组件样式定义 -->
    <style name="CustomButton" parent="Widget.AppCompat.Button.Borderless">
        <item name="android:textSize">14sp</item>
        <item name="android:padding">0dp</item>
        <item name="android:textColor">@color/colorPrimary</item>  <!-- 使用colorPrimary(#3F51B5)作为按钮文字颜色 -->
        <item name="android:drawableTint">@color/white</item>     <!-- 使用白色作为图标着色 -->
        <item name="android:minWidth">0dp</item>
        <item name="android:minHeight">0dp</item>
        <item name="android:includeFontPadding">false</item>
    </style>

</resources>