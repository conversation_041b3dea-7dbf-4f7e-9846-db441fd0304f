package com.autolink.sbjk.model.entity;

import java.util.ArrayList;
import java.util.List;

/**
 * 录制会话实体类
 * 表示一次完整的录制会话
 */
public class RecordingSession {
    
    private String sessionId;
    private String cameraId;
    private String cameraName;
    private long startTime;
    private long endTime;
    private String basePath;
    private List<VideoSegment> segments;
    private boolean isActive;
    private long totalDuration;
    private long totalFileSize;
    private String lastError;
    
    public RecordingSession(String sessionId, String cameraId) {
        this.sessionId = sessionId;
        this.cameraId = cameraId;
        this.cameraName = getCameraNameById(cameraId);
        this.startTime = System.currentTimeMillis();
        this.endTime = 0;
        this.segments = new ArrayList<>();
        this.isActive = true;
        this.totalDuration = 0;
        this.totalFileSize = 0;
    }
    
    // Getters
    public String getSessionId() { return sessionId; }
    public String getCameraId() { return cameraId; }
    public String getCameraName() { return cameraName; }
    public long getStartTime() { return startTime; }
    public long getEndTime() { return endTime; }
    public String getBasePath() { return basePath; }
    public List<VideoSegment> getSegments() { return segments; }
    public boolean isActive() { return isActive; }
    public long getTotalDuration() { return totalDuration; }
    public long getTotalFileSize() { return totalFileSize; }
    public String getLastError() { return lastError; }
    
    // Setters
    public void setEndTime(long endTime) { this.endTime = endTime; }
    public void setBasePath(String basePath) { this.basePath = basePath; }
    public void setActive(boolean active) { this.isActive = active; }
    public void setLastError(String error) { this.lastError = error; }
    
    /**
     * 添加视频片段
     */
    public void addSegment(VideoSegment segment) {
        if (segment != null) {
            segments.add(segment);
            totalDuration += segment.getDuration();
            totalFileSize += segment.getFileSize();
        }
    }
    
    /**
     * 移除视频片段
     */
    public boolean removeSegment(VideoSegment segment) {
        if (segment != null && segments.remove(segment)) {
            totalDuration -= segment.getDuration();
            totalFileSize -= segment.getFileSize();
            return true;
        }
        return false;
    }
    
    /**
     * 获取当前录制时长
     */
    public long getCurrentDuration() {
        if (isActive && startTime > 0) {
            return System.currentTimeMillis() - startTime;
        }
        return endTime > 0 ? endTime - startTime : 0;
    }
    
    /**
     * 获取片段数量
     */
    public int getSegmentCount() {
        return segments.size();
    }
    
    /**
     * 获取最新的片段
     */
    public VideoSegment getLatestSegment() {
        if (segments.isEmpty()) {
            return null;
        }
        return segments.get(segments.size() - 1);
    }
    
    /**
     * 结束录制会话
     */
    public void endSession() {
        this.isActive = false;
        this.endTime = System.currentTimeMillis();
    }
    
    /**
     * 检查会话是否有效
     */
    public boolean isValid() {
        return sessionId != null && !sessionId.isEmpty() && 
               cameraId != null && !cameraId.isEmpty() && 
               startTime > 0;
    }
    
    /**
     * 根据相机ID获取相机名称
     */
    private String getCameraNameById(String cameraId) {
        switch (cameraId) {
            case "26": return "前摄像头";
            case "30": return "后摄像头";
            case "34": return "左摄像头";
            case "38": return "右摄像头";
            default: return "摄像头" + cameraId;
        }
    }
    
    @Override
    public String toString() {
        return "RecordingSession{" +
                "sessionId='" + sessionId + '\'' +
                ", cameraId='" + cameraId + '\'' +
                ", cameraName='" + cameraName + '\'' +
                ", isActive=" + isActive +
                ", segmentCount=" + segments.size() +
                ", totalDuration=" + totalDuration +
                '}';
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        RecordingSession that = (RecordingSession) obj;
        return sessionId.equals(that.sessionId);
    }
    
    @Override
    public int hashCode() {
        return sessionId.hashCode();
    }
}
